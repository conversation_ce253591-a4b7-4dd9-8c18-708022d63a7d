package com.dtstack.dtcenter.common.loader.sqlserver;

public class SqlConstants {

    //函数常量
    public static final String FUNCTION_TYPE = "FUNCTION";
    //存储过程
    public static final String PROCEDURE_TYPE = "PROCEDURE";
    //表
    public static final String TABLE_TYPE = "TABLE";
    //视图
    public static final String VIEW_TYPE = "VIEW";
    public static final String VIEW = "'VIEW'";

    // 普通表
    public static final String BASE_TABLE = "'BASE TABLE'";

    /**
     * 获取所有库
     */
    public static final String ALL_DATABASE = "SELECT name FROM sys.databases WHERE name NOT IN ('master', 'tempdb', 'model', 'msdb')";

    /**
     * 获取指定库下的所有schema
     */

    public static final String ALL_SCHEMA = "SELECT name FROM sys.schemas WHERE name NOT IN ('guest', 'INFORMATION_SCHEMA', 'sys', 'db_owner', 'db_accessadmin', 'db_securityadmin', 'db_ddladmin', 'db_backupoperator', 'db_datareader', 'db_datawriter', 'db_denydatareader', 'db_denydatawriter')";



    // 获取指定数据库下的表
    public static final String GET_TABLE_SCHEMA_SQL = "SELECT \n" +
            "    TABLE_SCHEMA,\n" +
            "    TABLE_NAME\n" +
            "FROM INFORMATION_SCHEMA.TABLES\n" +
            "WHERE TABLE_SCHEMA = '%s'\n" +
            "  AND TABLE_TYPE = %s ";

    //表名模糊搜索sql
    // 表名正则匹配模糊查询
    public static final String SEARCH_SQL = " AND table_name LIKE  '%%%s%%'  ";

    //获取指定库下或者指定表下的索引
    public static final String GET_INDEX_SQL ="SELECT \n" +
            "    s.name AS schema_name,\n" +
            "    t.name AS table_name,\n" +
            "    i.name AS index_name,\n" +
            "    i.is_unique,\n" +
            "    i.type_desc AS index_type\n" +
            "FROM sys.indexes i\n" +
            "JOIN sys.tables t ON i.object_id = t.object_id\n" +
            "JOIN sys.schemas s ON t.schema_id = s.schema_id\n" +
            "WHERE i.is_hypothetical = 0 \n" +
            "  AND s.name = '%s' and  i.name is not null\n" ;

    /**
     * 索引搜索sql
     */
    public static final String SEARCH_INDEX_SQL = " AND i.name LIKE  '%%%s%%'  ";

    public static final String GROUP_INDEX_SQL = " GROUP BY s.name, t.name, i.name, i.is_unique, i.type_desc ";

    /**
     * 获取索引参数
     */
    public static final String GET_INDEX_COLUMN_SQL = "SELECT \n" +
            "    i.name AS INDEX_NAME,\n" +
            "    t.name AS TABLE_NAME,\n" +
            "    c.name AS COLUMN_NAME,\n" +
            "    ic.key_ordinal AS SEQ_IN_INDEX,\n" +
            "    ic.is_descending_key  AS collation,\n" +
            "    c.is_nullable  AS NULLABLE\n" +
            "FROM sys.indexes i\n" +
            "JOIN sys.index_columns ic \n" +
            "    ON i.object_id = ic.object_id \n" +
            "   AND i.index_id = ic.index_id\n" +
            "JOIN sys.columns c \n" +
            "    ON ic.object_id = c.object_id \n" +
            "   AND ic.column_id = c.column_id\n" +
            "JOIN sys.tables t \n" +
            "    ON t.object_id = i.object_id\n" +
            "JOIN sys.schemas s \n" +
            "    ON t.schema_id = s.schema_id\n" +
            "WHERE s.name = '%s'  \n" +
            "  AND t.name = '%s'   \n" +
            "  AND i.name = '%s'    \n" +
            "ORDER BY ic.key_ordinal" ;

    //获取指定库得函数或者存储过程
    public static final String GET_PRODUCE_SQL = "\n" +
            "SELECT\n" +
            "    s.name AS ROUTINE_SCHEMA,\n" +
            "    p.name AS ROUTINE_NAME,\n" +
            "    CASE \n" +
            "        WHEN p.type IN ('P', 'X') THEN 'PROCEDURE'\n" +
            "        WHEN p.type IN ('FN', 'IF', 'TF') THEN 'FUNCTION'\n" +
            "    END AS ROUTINE_TYPE,\n" +
            "    m.definition AS ROUTINE_DEFINITION\n" +
            "FROM sys.procedures p\n" +
            "JOIN sys.schemas s ON p.schema_id = s.schema_id\n" +
            "JOIN sys.sql_modules m ON p.object_id = m.object_id\n" +
            "WHERE s.name = '%s'\n" +
            "  AND (\n" +
            "       '%s' = 'PROCEDURE' AND p.type IN ('P', 'X')\n" +
            "    OR '%s' = 'FUNCTION' AND p.type IN ('FN', 'IF', 'TF')\n" +
            "  )" ;

    public static final String SEARCH_PRODUCE_SQL = " AND p.name LIKE  '%%%s%%'  ";



    //获取指定库得函数或者存储过程参数
    public static final String GET_PRODUCE_ARGUMENTS_SQL = "SELECT\n" +
            "    s.name AS SPECIFIC_SCHEMA,\n" +
            "    o.name AS SPECIFIC_NAME,\n" +
            "    p.name AS PARAMETER_NAME,\n" +
            "    t.name AS DATA_TYPE,\n" +
            "    p.parameter_id AS ORDINAL_POSITION,\n" +
            "    p.is_output AS PARAMETER_MODE,\n" +
            "    p.precision AS NUMERIC_PRECISION,\n" +
            "    p.scale AS NUMERIC_SCALE,\n" +
            "    p.max_length AS CHARACTER_OCTET_LENGTH\n" +
            "FROM sys.parameters p\n" +
            "JOIN sys.objects o ON p.object_id = o.object_id\n" +
            "JOIN sys.schemas s ON o.schema_id = s.schema_id\n" +
            "JOIN sys.types t ON p.user_type_id = t.user_type_id\n" +
            "WHERE s.name = '%s'\n" +
            "  AND o.name = '%s'" ;
    //获取函数或者存储过程的创建语句
    public static final String GET_F_P_CREATE_SQL="SELECT OBJECT_DEFINITION(OBJECT_ID('%s.%s')) AS ddl ";

}

