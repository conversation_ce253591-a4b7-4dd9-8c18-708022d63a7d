/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.dm;

import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.common.utils.SearchUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.DmSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Mysql5SourceDTO;
import com.dtstack.dtcenter.loader.dto.source.OracleSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 11:19 2020/4/17
 * @Description：达梦客户端
 */
@Slf4j
public class DmClient extends AbsRdbmsClient {
    private static final String SHOW_ALL_TABLES_SQL = "SELECT table_name FROM ALL_TABLES WHERE OWNER = '%s'";
    private static final String SHOW_WITH_VIEWS_SQL = "UNION SELECT view_name FROM ALL_VIEWS WHERE OWNER = '%s'";

    private static String DM_ALL_DATABASES = "SELECT DISTINCT OWNER FROM SYS.DBA_TABLES WHERE TABLESPACE_NAME = 'MAIN'";

    private static String CREATE_TABLE_SQL = "select dbms_metadata.get_ddl(OBJECT_TYPE => 'TABLE',\n" +
            "NAME=>upper('%s'),SCHNAME => '%s')";

    /**
     * 获取当前版本号
     */
    private static final String SHOW_VERSION = "select BANNER from v$version";

    /**
     * 获取oracle默认使用的schema
     */
    private static final String CURRENT_DB = "select sys_context('USERENV', 'CURRENT_SCHEMA') as schema_name from dual";

    /**
     * 获取表注释
     */
    private static final String COMMENTS_SQL = "SELECT COMMENTS FROM  all_tab_comments WHERE TABLE_NAME = '%s' ";
    /**
     * 表注释获取条件限制
     */
    private static final String COMMENTS_CONDITION_SQL = " AND OWNER = '%s' ";
    /**
     * 表注释字段
     */
    private static final String ORACLE_TABLE_COMMENT = "COMMENTS";
    /**
     * 限制条数语句
     */
    private static final String LIMIT_SQL = " AND ROWNUM <= %s ";

    @Override
    protected ConnFactory getConnFactory() {
        return new DmConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.DMDB;
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(iSource, queryDTO, false);

        DmSourceDTO dmSourceDTO = (DmSourceDTO) iSource;
        Statement statement = null;
        ResultSet rs = null;
        List<String> tableList = new ArrayList<>();
        try {
            String schema = queryDTO.getSchema();
            // schema若为空，则查询所有schema下的表
            String sql;
            if (StringUtils.isBlank(schema)) {
                log.info("schema is null，get all table！");
                schema = super.getCurrentDatabase(dmSourceDTO);
            }
            sql = queryDTO.getView() ? String.format(SHOW_ALL_TABLES_SQL + SHOW_WITH_VIEWS_SQL, schema, schema) : String.format(SHOW_ALL_TABLES_SQL, schema);
            log.info("current used schema：{}", schema);
            statement = dmSourceDTO.getConnection().createStatement();
            DBUtil.setFetchSize(statement, queryDTO);
            rs = statement.executeQuery(sql);
            while (rs.next()) {
                tableList.add(rs.getString(1));
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table exception,%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(dmSourceDTO, clearStatus));
        }
        return SearchUtil.handleSearchAndLimit(tableList, queryDTO);
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        DmSourceDTO dmSourceDTO = (DmSourceDTO) source;
        DmDownloader dmDownloader = new DmDownloader(getCon(dmSourceDTO), queryDTO.getSql(), dmSourceDTO.getSchema());
        dmDownloader.configure();
        return dmDownloader;
    }

    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        DmSourceDTO dmSourceDTO = (DmSourceDTO) source;
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        if(SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())|| SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())){
            String sql=String.format(SqlConstants.CREATE_FUNCTION_SQL,queryDTO.getTableName(),queryDTO.getSchema());
            log.error("获取函数或者存储过程的ddl:{}",sql);
            Statement statement = null;
            ResultSet rs = null;
            String createTableSql = null;
            try {
                statement = dmSourceDTO.getConnection().createStatement();
                rs = statement.executeQuery(sql);
                int columnSize = rs.getMetaData().getColumnCount();
                while (rs.next()) {
                    createTableSql = rs.getString(columnSize == 1 ? 1 : 2);
                    break;
                }
            } catch (Exception e) {
                throw new DtLoaderException(String.format("failed to get the create table sql：%s", e.getMessage()), e);
            } finally {
                DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(dmSourceDTO, clearStatus));
            }
            return createTableSql;
        }
        queryDTO.setSql(String.format(SqlConstants.CREATE_TABLE_SQL,queryDTO.getSchema(), queryDTO.getTableName()));
        return super.getCreateTableSql(source, queryDTO);
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    public String getShowDbSql() {
        return DM_ALL_DATABASES;
    }

    @Override
    public String getDbsSql() {
        return DM_ALL_DATABASES;
    }
    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    public String getCharacterCollation(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return null;
    }

    @Override
    public String getCharacterSet(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return null;
    }

    @Override
    public String getTableMetaComment(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        DmSourceDTO dmSourceDTO = (DmSourceDTO) sourceDTO;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : dmSourceDTO.getSchema();
        StringBuilder commentQuerySql = new StringBuilder();
        commentQuerySql.append(String.format(COMMENTS_SQL, queryDTO.getTableName()));
        if (StringUtils.isEmpty(schema)) {
            //若schema为空，则通过当前数据库的连接信息获取
            schema = super.getCurrentDatabase(dmSourceDTO);
        }
        commentQuerySql.append(String.format(COMMENTS_CONDITION_SQL, schema));
        List<Map<String, Object>> queryResult = executeQuery(dmSourceDTO, SqlQueryDTO.builder().sql(commentQuerySql.toString()).build());
        if (CollectionUtils.isEmpty(queryResult) || MapUtils.isEmpty(queryResult.get(0))) {
            return "";
        }
        return MapUtils.getString(queryResult.get(0), ORACLE_TABLE_COMMENT, "");
    }

    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("\"") || !tableName.endsWith("\"")) {
            tableName = String.format("\"%s\"", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("\"") || !schema.endsWith("\"")) {
            schema = String.format("\"%s\"", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }
    @Override
    public Integer getMaxConnections(ISourceDTO sourceDTO) {
        Integer result = null;
        try {
            List<Map<String, Object>> resultList = executeQuery(sourceDTO, SqlQueryDTO.builder().sql(SqlConstants.GET_MAX_CONNECTIONS).build());
            if (CollectionUtils.isNotEmpty(resultList)) {
                result = MapUtils.getInteger(resultList.get(0), "MAX_CONNECTIONS");
            }
        } catch (Exception e) {
            log.error("get max connections error", e);
        }
        return result;
    }

    @Override
    public Boolean getMetadataPrivileges(ISourceDTO sourceDTO) {
        Boolean result = null;
        DmSourceDTO dmSourceDTO = (DmSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, SqlQueryDTO.builder().build(), false);
        try {
            result = checkViaGrants(dmSourceDTO.getConnection()) || checkViaTestQuery(dmSourceDTO.getConnection());
        } catch (Exception e) {
            // 异常为无权限
            result = false;
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(dmSourceDTO, clearStatus));
        }
        return result;
    }

    private boolean checkViaGrants(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(SqlConstants.SHOW_GRANTS)) {
            Pattern globalSelectPattern = Pattern.compile(
                    "^GRANT .*\\bSELECT\\b.* ON \\*\\.\\* TO ",
                    Pattern.CASE_INSENSITIVE
            );
            while (rs.next()) {
                String grant = rs.getString(1);
                // 匹配是否存在全局 SELECT 权限
                if (globalSelectPattern.matcher(grant).find()) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkViaTestQuery(Connection conn) {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(SqlConstants.GET_METADATA_PRIVILEGES)) {
            // 查询成功 = 有权限
            return rs.next();
        } catch (SQLException e) {
            return false; // 查询失败 = 无权限
        }
    }

    /**
     * 获取表
     * @param sourceDTO 数据源描述对象，包含连接数据库所需的信息，如数据库类型、连接字符串等
     * @param queryDTO  SQL查询对象，包含查询数据库表所需的条件，如表名、schema名等
     * @return
     */
    @Override
    public List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        DmSourceDTO dmSourceDTO = (DmSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> dbTableVOS = new ArrayList<>();
        try {
            statement = dmSourceDTO.getConnection().createStatement();

            //判断搜查类型为空时查全部
            String sql = "";
            if (StringUtils.isEmpty(queryDTO.getType())) {
                sql = String.format(SqlConstants.GET_TABLE_SCHEMA_SQL_ALL, queryDTO.getSchema(), "");
            } else {
                String type = SqlConstants.BASE_TABLE;
                sql = String.format(SqlConstants.GET_TABLE_SQL, queryDTO.getSchema());
                if ("VIEW".equalsIgnoreCase(queryDTO.getType())) {
                    type = SqlConstants.VIEW;
                    sql = String.format(SqlConstants.GET_VIEW_SQL, queryDTO.getSchema());
                }
            }
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            log.info("getMedataDataTables sql:{}",sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO dbTableVO = new DbTableVO();
                dbTableVO.setDbName(queryDTO.getDbName());
                dbTableVO.setSchemaName(queryDTO.getSchema());
                dbTableVO.setName(resultSet.getString("TABLE_NAME"));
                String tableType = queryDTO.getType();
                dbTableVO.setType(tableType);
                dbTableVOS.add(dbTableVO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取表或者视图异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(dmSourceDTO, clearStatus));
        }
        return dbTableVOS;
    }

    /**
     * 获取索引信息
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    @Override
    public  List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        DmSourceDTO dmSourceDTO = (DmSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = dmSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //表名为空时  查所有
            if (StringUtils.isEmpty(queryDTO.getTableName())) {
                sql = String.format(SqlConstants.GET_INDEX_SQL, queryDTO.getSchema());
            } else {
                sql = String.format(SqlConstants.GET_INDEX_SQL_BY_TABLE, queryDTO.getSchema(), queryDTO.getTableName());
            }
            //是否有模糊搜索
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_INDEX_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            //拼上分组
            log.info("getIndexList SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(queryDTO.getDbName());
                indexMetaDTO.setSchemaName(queryDTO.getSchema());
                indexMetaDTO.setTableName(resultSet.getString("TABLE_NAME"));
                indexMetaDTO.setName(resultSet.getString("INDEX_NAME"));
                String indexType = resultSet.getString("INDEX_TYPE");
                indexMetaDTO.setIndexType(indexType);
                indexMetaDTO.setType(queryDTO.getType());
                indexMetaDTO.setUnique(resultSet.getInt("is_unique"));
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(dmSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public   List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        DmSourceDTO dmSourceDTO = (DmSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = dmSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String  sql = String.format(SqlConstants.GET_INDEX_COLUMN_SQL, queryDTO.getSchema(), queryDTO.getTableName(),queryDTO.getIndexName());
            log.info("getIndexColumn SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                indexMetaDTO.setKey(resultSet.getString("COLUMN_NAME"));
                indexMetaDTO.setColumnOrder(resultSet.getInt("SEQ_IN_INDEX"));
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引字段异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(dmSourceDTO, clearStatus));
        }
        return indexCols;
    }


    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        DmSourceDTO dmSourceDTO = (DmSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = dmSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //type 为函数还是存储过程
            if(SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())){
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getSchema(),SqlConstants.FUNCTION_TYPE);
            }

            if(SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())){
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getSchema(),SqlConstants.PROCEDURE_TYPE);
            }
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_PRODUCE_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }

            log.info("getFunctionList SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(queryDTO.getDbName());
                indexMetaDTO.setSchemaName(queryDTO.getSchema());
                indexMetaDTO.setName(resultSet.getString("ROUTINE_NAME"));
                String indexType = resultSet.getString("ROUTINE_TYPE");
                indexMetaDTO.setType(indexType);
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(dmSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        DmSourceDTO dmSourceDTO = (DmSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = dmSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String  sql = String.format(SqlConstants.GET_PRODUCE_ARGUMENTS_SQL, queryDTO.getSchema(),queryDTO.getObjectName());
            log.info("getFunctionArguments SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                String parameterName = resultSet.getString("PARAMETER_NAME");
                if(StringUtils.isEmpty(parameterName)){
                    parameterName="RETURN";
                }
                indexMetaDTO.setKey(parameterName);
                indexMetaDTO.setType(resultSet.getString("DATA_TYPE"));
                String parameterMode = resultSet.getString("PARAMETER_MODE");
                if(StringUtils.isEmpty(parameterMode)){
                    parameterMode="RETURN";
                }
                indexMetaDTO.setInOut(parameterMode);
                indexMetaDTO.setColumnOrder(resultSet.getInt("ORDINAL_POSITION"));
                int characterOctetLength = resultSet.getInt("CHARACTER_OCTET_LENGTH");
                int numericPrecision = resultSet.getInt("NUMERIC_PRECISION");
                if(characterOctetLength>0){
                    indexMetaDTO.setLength(characterOctetLength);
                }else{
                    indexMetaDTO.setLength(numericPrecision);
                    indexMetaDTO.setPrecision(numericPrecision);
                    indexMetaDTO.setScale(resultSet.getInt("NUMERIC_SCALE"));
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(dmSourceDTO, clearStatus));
        }
        return indexCols;
    }


}
