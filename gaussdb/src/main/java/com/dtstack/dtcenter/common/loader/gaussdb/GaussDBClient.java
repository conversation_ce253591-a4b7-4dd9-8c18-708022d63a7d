/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.gaussdb;

import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.GaussDBSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.PostgresqlSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 15:52 2020/1/7
 * @Description：GaussDB 客户端
 */
@Slf4j
public class GaussDBClient extends AbsRdbmsClient {
    // 模糊查询数据库
    private static final String SHOW_DB_LIKE = "SELECT u.datname FROM pg_catalog.pg_database u where u.datname='%s'";

    private static final String SMALLSERIAL = "smallserial";

    private static final String SERIAL = "serial";

    private static final String BIGSERIAL = "bigserial";

    private static final String DATABASE_QUERY = "select nspname from pg_namespace";

    private static final String DONT_EXIST = "doesn't exist";

    // 获取指定schema下的表，包括视图
    private static final String SHOW_TABLE_AND_VIEW_BY_SCHEMA_SQL = "SELECT table_name FROM information_schema.tables WHERE table_schema = '%s' %s";

    // 获取指定schema下的表，不包括视图
    private static final String SHOW_TABLE_BY_SCHEMA_SQL = "SELECT table_name FROM information_schema.tables WHERE table_schema = '%s' AND table_type = 'BASE TABLE' %s";

    //获取所有表名，包括视图，表名前拼接schema，并对schema和tableName进行增加双引号处理
    private static final String ALL_TABLE_AND_VIEW_SQL = "SELECT '\"'||table_schema||'\".\"'||table_name||'\"' AS schema_table FROM information_schema.tables WHERE 1 = 1 %s order by schema_table ";

    //获取所有表名，不包括视图，表名前拼接schema，并对schema和tableName进行增加双引号处理
    private static final String ALL_TABLE_SQL = "SELECT '\"'||table_schema||'\".\"'||table_name||'\"' AS schema_table FROM information_schema.tables WHERE table_type = 'BASE TABLE' %s order by schema_table ";

    // 获取正在使用数据库
    private static final String CURRENT_DB = "select current_database()";

    // 获取正在使用 schema
    private static final String CURRENT_SCHEMA = "select current_schema()";

    // 根据schema选表表名模糊查询
    private static final String SEARCH_SQL = " AND table_name LIKE '%s' ";

    // 限制条数语句
    private static final String LIMIT_SQL = " LIMIT %s ";

    // 获取当前版本号
    private static final String SHOW_VERSION = "show server_version";

    // 创建 schema
    private static final String CREATE_SCHEMA_SQL_TMPL = "create schema %s ";

    // 查询表注释
    private static final String TABLE_COMMENT = "select relname as tabname, cast(obj_description(oid,'pg_class') as varchar) as comment from pg_class c where relname = '%s' %s";

    // 查询字段注释
    private static final String COLUMN_COMMENT = "SELECT A.attname AS column,D.description AS comment FROM pg_class C,pg_attribute A,pg_description D WHERE C.relname = '%s' %s AND A.attnum > 0 AND A.attrelid = C.oid AND D.objoid = A.attrelid AND D.objsubid = A.attnum";

    // 查询 schema 的 oid（主键）
    private static final String SCHEMA_RECORD_OID = " and relnamespace in (select oid from pg_namespace where nspname = '%s')";

    // 获取指定 schema 下面表的字段信息
    private static final String SHOW_TABLE_COLUMN_BY_SCHEMA = "SELECT array_to_string(ARRAY(select concat( c1, c2, c3, c4) as column_line from (select column_name || ' ' || data_type as c1,case when character_maximum_length > 0 then '(' || character_maximum_length || ')' end as c2,case when is_nullable = 'NO' then ' NOT NULL' end as c3,case when column_default is not Null then ' DEFAULT' end || ' ' || replace(column_default, '::character varying', '') as c4 from information_schema.columns where table_name = '%1$s' and table_schema='%2$s' order by ordinal_position) as string_columns), ',') as column";

    // 获取指定表的字段信息
    private static final String SHOW_TABLE_COLUMN = "SELECT array_to_string(ARRAY(select concat( c1, c2, c3, c4) as column_line from (select column_name || ' ' || data_type as c1,case when character_maximum_length > 0 then '(' || character_maximum_length || ')' end as c2,case when is_nullable = 'NO' then ' NOT NULL' end as c3,case when column_default is not Null then ' DEFAULT' end || ' ' || replace(column_default, '::character varying', '') as c4 from information_schema.columns where table_name = '%1$s' order by ordinal_position) as string_columns), ',') as column";

    // 获取指定 schema 下面表的约束
    private static final String SHOW_TABLE_CONSTRAINT_BY_SCHEMA = "select array_to_string(\n" +
            "array(\n" +
            "select concat(' CONSTRAINT ',conname ,c,u,p,f)   from (\n" +
            "select conname,\n" +
            "case when contype='c' then ' CHECK('|| consrc ||')' end  as c  ,\n" +
            "case when contype='u' then ' UNIQUE('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = (select oid from pg_class where relname= '%1$s' and relnamespace = (select oid from pg_namespace where nspname = '%2$s')) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'u' AND C.conrelid = ((select oid from pg_class where relname= '%1$s' and relnamespace = (select oid from pg_namespace where nspname = '%2$s'))) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',') ) ||')' end as u ,\n" +
            "case when contype='p' then ' PRIMARY KEY ('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = (select oid from pg_class where relname= '%1$s' and relnamespace = (select oid from pg_namespace where nspname = '%2$s')) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'p' AND C.conrelid = ((select oid from pg_class where relname= '%1$s' and relnamespace = (select oid from pg_namespace where nspname = '%2$s'))) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',')) ||')' end  as p  ,\n" +
            "case when contype='f' then ' FOREIGN KEY('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = (select oid from pg_class where relname= '%1$s' and relnamespace = (select oid from pg_namespace where nspname = '%2$s')) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'u' AND C.conrelid = ((select oid from pg_class where relname= '%1$s' and relnamespace = (select oid from pg_namespace where nspname = '%2$s'))) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',')) ||') REFERENCES '|| \n" +
            "(select p.relname from pg_class p where p.oid=c.confrelid )  || '('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = ((select oid from pg_class where relname= '%1$s' and relnamespace = (select oid from pg_namespace where nspname = '%2$s'))) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'u' AND C.conrelid = (select oid from pg_class where relname= '%1$s' and relnamespace = (select oid from pg_namespace where nspname = '%2$s')) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',') ) ||')' end as  f\n" +
            "from pg_constraint c\n" +
            "where contype in('u','c','f','p') and conrelid=( \n" +
            "select oid  from pg_class  where relname='%1$s' and relnamespace = (select oid from pg_namespace where nspname = '%2$s') \n" +
            " )\n" +
            ") as t  \n" +
            ") ,',' ) as constraint";

    // 获取指定 schema 下面表的约束
    private static final String SHOW_TABLE_CONSTRAINT = "select array_to_string(\n" +
            "array(\n" +
            "select concat(' CONSTRAINT ',conname ,c,u,p,f)   from (\n" +
            "select conname,\n" +
            "case when contype='c' then ' CHECK('|| consrc ||')' end  as c  ,\n" +
            "case when contype='u' then ' UNIQUE('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = (select oid from pg_class where relname= '%1$s' ) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'u' AND C.conrelid = ((select oid from pg_class where relname= '%1$s' )) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',') ) ||')' end as u ,\n" +
            "case when contype='p' then ' PRIMARY KEY ('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = (select oid from pg_class where relname= '%1$s' ) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'p' AND C.conrelid = ((select oid from pg_class where relname= '%1$s' )) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',')) ||')' end  as p  ,\n" +
            "case when contype='f' then ' FOREIGN KEY('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = (select oid from pg_class where relname= '%1$s' ) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'u' AND C.conrelid = ((select oid from pg_class where relname= '%1$s' )) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',')) ||') REFERENCES '|| \n" +
            "(select p.relname from pg_class p where p.oid=c.confrelid )  || '('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = ((select oid from pg_class where relname= '%1$s' )) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'u' AND C.conrelid = (select oid from pg_class where relname= '%1$s' ) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',') ) ||')' end as  f\n" +
            "from pg_constraint c\n" +
            "where contype in('u','c','f','p') and conrelid=( \n" +
            "select oid  from pg_class  where relname='%1$s' \n" +
            " )\n" +
            ") as t  \n" +
            ") ,',' ) as constraint";

    // 格式刷 schema 和 表名
    private static final String SCHEMA_TABLE_FORMAT = "\"%s\".\"%s\"";

    // 建表模版
    private static final String CREATE_TABLE_TEMPLATE = "CREATE TABLE %s (%s);";

    //pg 表数据量查询
    private static final String PG_TABLE_ROW = "SELECT reltuples FROM pg_class r JOIN pg_namespace n ON (relnamespace = n.oid) WHERE relkind = 'r' AND n.nspname = '%s' and relname ='%s'";

    //获取字符集 和 排序规则
    private static final String GET_PG_CHARACTER_INFO = "select pg_encoding_to_char(encoding),datcollate from pg_database where datname = '%s' ";

    private static final String JDBC_URL = "jdbc:postgresql://%s:%s/%s";

    // 判断table是否在schema中
    private static final String TABLE_IS_IN_SCHEMA = " SELECT table_name FROM information_schema.tables WHERE table_schema = '%s' and table_name = '%s' ";


    @Override
    protected ConnFactory getConnFactory() {
        return new GaussDBConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.GaussDB;
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) iSource;
        Integer clearStatus = beforeQuery(gaussDBSourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet rs = null;
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            if (Objects.nonNull(queryDTO) && Objects.nonNull(queryDTO.getLimit())) {
                // 设置最大条数
                statement.setMaxRows(queryDTO.getLimit());
            }
            StringBuilder constr = new StringBuilder();
            if (Objects.nonNull(queryDTO) && StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
                constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
            }
            //大小写区分，不传schema默认获取所有表，并且表名签名拼接schema，格式："schema"."tableName"
            String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : gaussDBSourceDTO.getSchema();
            String querySql;
            if (StringUtils.isNotBlank(schema)) {
                querySql = queryDTO.getView() ? String.format(SHOW_TABLE_AND_VIEW_BY_SCHEMA_SQL, schema, constr.toString()) : String.format(SHOW_TABLE_BY_SCHEMA_SQL, schema, constr.toString());
            } else {
                querySql = queryDTO.getView() ? String.format(ALL_TABLE_AND_VIEW_SQL, constr.toString()) : String.format(ALL_TABLE_SQL, constr.toString());
            }
            DBUtil.setFetchSize(statement, queryDTO);
            rs = statement.executeQuery(querySql);
            List<String> tableList = new ArrayList<>();
            while (rs.next()) {
                tableList.add(rs.getString(1));
            }
            return tableList;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table exception,%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
    }

    @Override
    public String getCharacterSet(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getGetPgCharacterInfo(source, queryDTO, true);
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getGetPgCharacterInfo(source, queryDTO, false);
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(gaussDBSourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        long tableRow = 0L;
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(PG_TABLE_ROW, queryDTO.getSchema(), queryDTO.getTableName()));
            if (tableRow == 0L) {
                resultSet = statement.executeQuery(String.format("select COUNT(1) from %s.%s", queryDTO.getSchema(), queryDTO.getTableName()));
                while (resultSet.next()) {
                    tableRow = resultSet.getInt(1);
                    return tableRow;
                }
            } else {
                return tableRow;
            }

        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return tableRow;
    }

    private String getGetPgCharacterInfo(ISourceDTO iSource, SqlQueryDTO queryDTO, Boolean characterSet) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) iSource;
        Integer clearStatus = beforeQuery(gaussDBSourceDTO, queryDTO, false);
        String currentDatabase = getCurrentDatabase(iSource);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(GET_PG_CHARACTER_INFO, currentDatabase));
            while (resultSet.next()) {
                if (characterSet) {
                    return resultSet.getString(1);
                } else {
                    return resultSet.getString(2);
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("获取pg系统参数异常，%s",
                    e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(gaussDBSourceDTO, queryDTO);

        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : gaussDBSourceDTO.getSchema();
            String schemaOidSql = StringUtils.isNotBlank(schema) ? String.format(SCHEMA_RECORD_OID, schema) : "";
            resultSet = statement.executeQuery(String.format(TABLE_COMMENT, queryDTO.getTableName(), schemaOidSql));
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);
                if (dbTableName.equalsIgnoreCase(queryDTO.getTableName())) {
                    return resultSet.getString(2);
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(sourceDTO, SqlQueryDTO.builder().build(), false);
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : sourceDTO.getSchema();
        String columnCommentSql = StringUtils.isNoneBlank(schema) ? String.format(COLUMN_COMMENT, queryDTO.getTableName(), String.format(SCHEMA_RECORD_OID, schema)) : String.format(COLUMN_COMMENT, queryDTO.getTableName(), "");
        Map<String, String> comments = Maps.newHashMap();
        try {
            List<Map<String, Object>> result = executeQuery(sourceDTO, SqlQueryDTO.builder().sql(columnCommentSql).build());
            for (Map<String, Object> row : result) {
                comments.put(MapUtils.getString(row, "column"), MapUtils.getString(row, "comment"));
            }
        } finally {
            DBUtil.clearAfterGetConnection(sourceDTO, clearStatus);
        }
        return comments;
    }

    @Override
    protected String getDbSeparator() {
        return "\"";
    }

    @Override
    protected String doDealType(ResultSetMetaData rsMetaData, Integer los) throws SQLException {
        String type = super.doDealType(rsMetaData, los);

        // smallserial、serial、bigserial 需要转换
        if (SMALLSERIAL.equalsIgnoreCase(type)) {
            return "int2";
        }
        if (SERIAL.equalsIgnoreCase(type)) {
            return "int4";
        }
        if (BIGSERIAL.equalsIgnoreCase(type)) {
            return "int8";
        }

        return type;
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) source;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : gaussDBSourceDTO.getSchema();
        GaussDBDownloader postgresqlDownloader = new GaussDBDownloader(getCon(gaussDBSourceDTO), queryDTO.getSql(), schema);
        postgresqlDownloader.configure();
        return postgresqlDownloader;
    }

    @Override
    public List<ColumnMetaDTO> getFlinkColumnMetaData(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(source, queryDTO);
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) source;
        Statement statement = null;
        ResultSet rs = null;
        List<ColumnMetaDTO> columns = new ArrayList<>();
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            String queryColumnSql = "select * from " + transferSchemaAndTableName(gaussDBSourceDTO, queryDTO)
                    + " where 1=2";
            rs = statement.executeQuery(queryColumnSql);
            ResultSetMetaData rsMetaData = rs.getMetaData();
            for (int i = 0, len = rsMetaData.getColumnCount(); i < len; i++) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setKey(rsMetaData.getColumnName(i + 1));
                String type = rsMetaData.getColumnTypeName(i + 1);
                int columnType = rsMetaData.getColumnType(i + 1);
                int precision = rsMetaData.getPrecision(i + 1);
                int scale = rsMetaData.getScale(i + 1);
                //postgresql类型转换
                String flinkSqlType = GaussDBAdapter.mapColumnTypeJdbc2Java(columnType, precision, scale);
                if (StringUtils.isNotEmpty(flinkSqlType)) {
                    type = flinkSqlType;
                }
                columnMetaDTO.setType(type);
                // 获取字段精度
                if (columnMetaDTO.getType().equalsIgnoreCase("decimal")
                        || columnMetaDTO.getType().equalsIgnoreCase("float")
                        || columnMetaDTO.getType().equalsIgnoreCase("double")
                        || columnMetaDTO.getType().equalsIgnoreCase("numeric")) {
                    columnMetaDTO.setScale(rsMetaData.getScale(i + 1));
                    columnMetaDTO.setPrecision(rsMetaData.getPrecision(i + 1));
                }
                columns.add(columnMetaDTO);
            }
            return columns;

        } catch (SQLException e) {
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get meta information for the fields of table :%s. Please contact the DBA to check the database table information.", queryDTO.getTableName()), e);
            }
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }

    }
    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) source;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : gaussDBSourceDTO.getSchema();
        if(SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())|| SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())){
            return getProduceSql(source, queryDTO);
        }
        List<Map<String, Object>> columnResult;
        List<Map<String, Object>> constraintResult;
        String tableName;
        if (StringUtils.isNotBlank(schema)) {
            try {
                columnResult = executeQuery(gaussDBSourceDTO, SqlQueryDTO.builder().sql(String.format(SHOW_TABLE_COLUMN_BY_SCHEMA, queryDTO.getTableName(), schema)).build());
                constraintResult = executeQuery(gaussDBSourceDTO, SqlQueryDTO.builder().sql(String.format(SqlConstants.SHOW_TABLE_CONSTRAINT_BY_SCHEMA_NEW, queryDTO.getTableName(), schema)).build());
                tableName = String.format(SCHEMA_TABLE_FORMAT, schema, queryDTO.getTableName());
            } finally {
                DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
            }
        } else {
            try {
                columnResult = executeQuery(gaussDBSourceDTO, SqlQueryDTO.builder().sql(String.format(SHOW_TABLE_COLUMN, queryDTO.getTableName())).build());
                constraintResult = executeQuery(gaussDBSourceDTO, SqlQueryDTO.builder().sql(String.format(SHOW_TABLE_CONSTRAINT, queryDTO.getTableName())).build());
                tableName = queryDTO.getTableName();
            } finally {
                DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
            }
        }
        if (CollectionUtils.isEmpty(columnResult) || StringUtils.isBlank(MapUtils.getString(columnResult.get(0), "column"))) {
            throw new DtLoaderException(String.format("Failed to get table %s field", queryDTO.getTableName()));
        }
        String columnStr = MapUtils.getString(columnResult.get(0), "column");
        String constraint = null;
        if (CollectionUtils.isNotEmpty(constraintResult)) {
            constraint = MapUtils.getString(constraintResult.get(0), "constraint");
        }
        if (StringUtils.isNotBlank(constraint)) {
            return String.format(CREATE_TABLE_TEMPLATE, tableName, columnStr + " , " + constraint);
        }
        return String.format(CREATE_TABLE_TEMPLATE, tableName, columnStr);
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }
    @Override
    public String getShowDbSql() {
        return DATABASE_QUERY;
    }

    /**
     * 获取所有 数据库 sql语句
     *
     * @return
     */
    @Override
    protected String getDbsSql() {
        return SqlConstants.DATABASE_QUERY;
    }


    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) sourceDTO;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : rdbmsSourceDTO.getSchema();
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            throw new DtLoaderException("schema is not empty...");
        }
        log.info("current used schema：{}", schema);
        StringBuilder constr = new StringBuilder();
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            constr.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        return String.format(SHOW_TABLE_BY_SCHEMA_SQL, schema, constr.toString());
    }

    /**
     * 处理Postgresql schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema
     * @param tableName
     * @return
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        return String.format("\"%s\".\"%s\"", schema, tableName);
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    protected String getCurrentSchemaSql() {
        return CURRENT_SCHEMA;
    }

    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    protected String getCreateDatabaseSql(String dbName, String comment) {
        return String.format(CREATE_SCHEMA_SQL_TMPL, dbName);
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name is not empty");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(SHOW_DB_LIKE, dbName)).build()));
    }


    /**
     * 函数和存储过程得sql
     */
    public String getProduceSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) sourceDTO;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : gaussDBSourceDTO.getSchema();
        String sql = String.format(SqlConstants.CREATE_FUNCTION_SQL, queryDTO.getTableName(), schema);
        log.error("获取函数或者存储过程的ddl:{}", sql);
        Statement statement = null;
        ResultSet rs = null;
        String createTableSql = null;
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            rs = statement.executeQuery(sql);
            int columnSize = rs.getMetaData().getColumnCount();
            while (rs.next()) {
                createTableSql = rs.getString(columnSize == 1 ? 1 : 2);
                break;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("failed to get the create table sql：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return createTableSql;
    }


    @Override
    public Integer getMaxConnections(ISourceDTO sourceDTO) {
        Integer result = null;
        try {
            List<Map<String, Object>> resultList = executeQuery(sourceDTO, SqlQueryDTO.builder().sql(SqlConstants.GET_MAX_CONNECTIONS).build());
            if (CollectionUtils.isNotEmpty(resultList)) {
                result = MapUtils.getInteger(resultList.get(0), "setting");
            }
        } catch (Exception e) {
            log.error("get max connections error", e);
        }
        return result;
    }


    /**
     * 获取表
     *
     * @param sourceDTO 数据源描述对象，包含连接数据库所需的信息，如数据库类型、连接字符串等
     * @param queryDTO  SQL查询对象，包含查询数据库表所需的条件，如表名、schema名等
     * @return
     */
    @Override
    public List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> dbTableVOS = new ArrayList<>();
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();

            //判断搜查类型为空时查全部
            String sql = "";
            String type = SqlConstants.BASE_TABLE;
            if (SqlConstants.VIEW_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                type = SqlConstants.VIEW;
            }
            sql = String.format(SqlConstants.GET_TABLE_SCHEMA_SQL, queryDTO.getSchema(), type, "");
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String format = String.format(SqlConstants.SEARCH_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO dbTableVO = new DbTableVO();
                dbTableVO.setDbName(queryDTO.getDbName());
                dbTableVO.setSchemaName(queryDTO.getSchema());
                dbTableVO.setName(resultSet.getString("TABLE_NAME"));
                String tableType = resultSet.getString("table_type");
                if ("BASE TABLE".equalsIgnoreCase(tableType)) {
                    tableType = SqlConstants.TABLE_TYPE;
                }
                dbTableVO.setType(tableType);
                dbTableVOS.add(dbTableVO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取表或者视图异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return dbTableVOS;
    }


    /**
     * 获取索引信息
     *
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    @Override
    public List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //表名为空时  查所有
            if (StringUtils.isEmpty(queryDTO.getTableName())) {
                sql = String.format(SqlConstants.GET_INDEX_SQL, queryDTO.getSchema());
            } else {
                sql = String.format(SqlConstants.GET_INDEX_SQL_BY_TABLE, queryDTO.getSchema(), queryDTO.getTableName());
            }
            //是否有模糊搜索
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String format = String.format(SqlConstants.SEARCH_INDEX_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            log.info("getIndexList SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            // 正则表达式解析索引信息
            String regex = "(?i)CREATE\\s+(UNIQUE\\s+)?INDEX\\s+(\\S+)\\s+ON\\s+(\\S+)\\.(\\S+)\\s+USING\\s+(\\S+)\\s*\\(([^)]+)\\)";
            Pattern pattern = Pattern.compile(regex);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(queryDTO.getDbName());
                indexMetaDTO.setSchemaName(queryDTO.getSchema());
                indexMetaDTO.setTableName(resultSet.getString("TABLE_NAME"));
                indexMetaDTO.setName(resultSet.getString("INDEX_NAME"));
                //获取ddl
                String indexDefinition = resultSet.getString("index_definition");
                indexMetaDTO.setDdl(indexDefinition);
                //解析ddl获取索引是否唯一、索引类型
                if (StringUtils.isNotEmpty(indexDefinition)) {
                    try {
                        Matcher matcher = pattern.matcher(indexDefinition);
                        if (matcher.find()) {
                            boolean isUnique = matcher.group(1) != null; // 是否唯一索引
                            indexMetaDTO.setUnique(isUnique ? 0 : 1);
                            String indexType = matcher.group(5);  // 索引类型 (btree, hash, gist, gin)
                            indexMetaDTO.setIndexType(indexType);
                        }
                    } catch (Exception e) {
                        log.error("解析索引信息异常{}", e.getMessage());
                    }
                }
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = String.format(SqlConstants.GET_INDEX_COLUMN_SQL, queryDTO.getSchema(), queryDTO.getTableName(), queryDTO.getIndexName());
            log.info("getIndexColumn SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                indexMetaDTO.setKey(resultSet.getString("column_name"));
                indexMetaDTO.setColumnOrder(resultSet.getInt("seq_in_index"));
                indexMetaDTO.setIndexType(resultSet.getString("index_type"));
                //是否为空
                String isNullable = resultSet.getString("is_nullable");
                if ("[v]".equalsIgnoreCase(isNullable)) {
                    indexMetaDTO.setNotNullFlag(true);
                } else {
                    indexMetaDTO.setNotNullFlag(false);
                }
                //是否唯一
                String isUnique = resultSet.getString("is_unique");
                if ("[v]".equalsIgnoreCase(isUnique)) {
                    indexMetaDTO.setUniqueFlag(true);
                } else {
                    indexMetaDTO.setUniqueFlag(false);
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引字段异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return indexCols;
    }


    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //type 为函数还是存储过程
            if (SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getSchema(), SqlConstants.FUNCTION_TYPE);
            }

            if (SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getSchema(), SqlConstants.PROCEDURE_TYPE);
            }
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String format = String.format(SqlConstants.SEARCH_PRODUCE_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }

            log.info("getFunctionList SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(queryDTO.getDbName());
                indexMetaDTO.setSchemaName(queryDTO.getSchema());
                indexMetaDTO.setName(resultSet.getString("ROUTINE_NAME"));
                String indexType = resultSet.getString("ROUTINE_TYPE");
                indexMetaDTO.setType(indexType);
                indexMetaDTO.setDdl(resultSet.getString("ROUTINE_DEFINITION"));
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = gaussDBSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = String.format(SqlConstants.GET_PRODUCE_ARGUMENTS_SQL, queryDTO.getSchema(), queryDTO.getObjectName()).toLowerCase();
            log.info("getFunctionArguments SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                String parameterName = resultSet.getString("parameter_name");
                indexMetaDTO.setKey(parameterName);
                indexMetaDTO.setType(resultSet.getString("data_type"));
                String parameterMode = resultSet.getString("parameter_mode");
                indexMetaDTO.setInOut(parameterMode);
                indexMetaDTO.setColumnOrder(resultSet.getInt("ordinal_position"));
                int characterOctetLength = resultSet.getInt("character_maximum_length");
                indexMetaDTO.setLength(characterOctetLength);
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return indexCols;
    }


    @Override
    public List<String> isTablesExists(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        List<String> missingEntries = new ArrayList<>();

        try {
            Connection connection = gaussDBSourceDTO.getConnection();

            // 构建 SQL 语句
            StringBuilder sql = new StringBuilder(
                    "SELECT table_schema, table_name " +
                            "FROM information_schema.tables " +
                            "WHERE (table_schema, table_name) = ANY(ARRAY[");

            List<String[]> parsedNames = new ArrayList<>();
            for (String input : queryDTO.getTables()) {
                String[] parts = input.split("\\.");
                if (parts.length == 2) {
                    parsedNames.add(parts);
                    sql.append("(?, ?),");
                }
            }

            if (parsedNames.isEmpty()) {
                return missingEntries; // 没有表需要检查，直接返回
            }

            sql.setLength(sql.length() - 1); // 移除最后一个逗号
            sql.append("]::text[])"); // 适配 PostgreSQL 数组查询

            // 如果指定了类型，则加上过滤条件
            if (!StringUtils.isEmpty(queryDTO.getType())) {
                sql.append(" AND table_type = ?");
            }

            preparedStatement = connection.prepareStatement(sql.toString());

            // 设置参数
            int index = 1;
            for (String[] name : parsedNames) {
                preparedStatement.setString(index++, name[0]); // table_schema
                preparedStatement.setString(index++, name[1]); // table_name
            }
            if (!StringUtils.isEmpty(queryDTO.getType())) {
                preparedStatement.setString(index,
                        SqlConstants.VIEW_TYPE.equalsIgnoreCase(queryDTO.getType()) ? "VIEW" : "BASE TABLE"
                );
            }

            resultSet = preparedStatement.executeQuery();

            // 记录已存在的表和视图
            Set<String> existingTables = new HashSet<>();
            while (resultSet.next()) {
                String schema = resultSet.getString("table_schema");
                String table = resultSet.getString("table_name");
                existingTables.add(schema + "." + table);
            }

            // 找出不存在的表或视图
            for (String name : queryDTO.getTables()) {
                if (!existingTables.contains(name)) {
                    missingEntries.add(name);
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException("获取不存在的表或视图失败", e);
        } finally {
            DBUtil.closeDBResources(resultSet, preparedStatement, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return missingEntries;
    }

    @Override
    public Boolean getMetadataPrivileges(ISourceDTO sourceDTO) {
        Boolean result = null;
        GaussDBSourceDTO gaussDBSourceDTO = (GaussDBSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, SqlQueryDTO.builder().build(), false);
        try {
            return queryAccess(gaussDBSourceDTO.getConnection(), SqlConstants.GET_SUPER_USER) || queryAccess(gaussDBSourceDTO.getConnection(),  SqlConstants.GET_PG_CATALOG_USAGE) || testMetadataQuery(gaussDBSourceDTO.getConnection());
        } catch (Exception e) {
            // 异常为无权限
            result = false;
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(gaussDBSourceDTO, clearStatus));
        }
        return result;
    }

    private boolean queryAccess(Connection conn, String sql) throws SQLException {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            return rs.next() && rs.getBoolean(1);
        }
    }

    private boolean testMetadataQuery(Connection conn) {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery( SqlConstants.GET_PG_CATALOG_TABLE_EXISTS)) {
            return rs.next(); // 查询成功则返回 true
        } catch (SQLException e) {
            return false; // 权限不足时抛出异常
        }
    }

    protected String getJdbcUrl( DatasourceInfoImportVO datasourceInfoImportVO) {
        String jdbcUrl = String.format(JDBC_URL, datasourceInfoImportVO.getIp(), datasourceInfoImportVO.getPort(),datasourceInfoImportVO.getDbName());
        return jdbcUrl;
    }
    @Override
    public String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {
        String sql = String.format(TABLE_IS_IN_SCHEMA, queryDTO.getSchema(), queryDTO.getTableName());
        return sql;
    }
}
