package com.dtstack.dtcenter.common.loader.sqlserver2017;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;

@Slf4j
public class SqlServerDDLGenerator {
    public static String generateTableDDL(Connection connection, String schema, String tableName) throws SQLException {
        StringBuilder ddl = new StringBuilder();
        DatabaseMetaData metaData = connection.getMetaData();
        String catalog = connection.getCatalog();

        // 获取主键信息（名称+列）
        Map<String, List<String>> primaryKey = getPrimaryKeyInfo(connection, schema, tableName);

        // 获取唯一约束
        Map<String, List<String>> uniqueConstraints = getUniqueConstraints(connection, schema, tableName);

        // 构建CREATE TABLE基础部分
        ddl.append("CREATE TABLE [").append(schema).append("].[").append(tableName).append("] (\n");

        // 处理列定义
        List<String> columnDefs = new ArrayList<>();
        // 获取注释信息
        Map<String, String> columnComments = getComments(connection, schema, tableName);
        String tableComment = getTableComment(connection, schema, tableName);
        try (ResultSet columns = metaData.getColumns(catalog, schema, tableName, "%")) {
            while (columns.next()) {
                columnDefs.add(buildColumnDefinition(columns, primaryKey,columnComments));
            }
        }
        ddl.append(String.join(",\n", columnDefs));

        // 添加主键约束
        if (!primaryKey.isEmpty()) {
            String pkName = primaryKey.keySet().iterator().next();
            List<String> pkColumns = primaryKey.get(pkName);
            ddl.append(",\n  CONSTRAINT [").append(pkName).append("] PRIMARY KEY (")
                    .append(String.join(", ", pkColumns)).append(")");
        }

        // 添加唯一约束
        for (Map.Entry<String, List<String>> entry : uniqueConstraints.entrySet()) {
            ddl.append(",\n  CONSTRAINT [").append(entry.getKey()).append("] UNIQUE (")
                    .append(String.join(", ", entry.getValue())).append(")");
        }

        // 处理外部表特殊属性
        if (isExternalTable(connection, schema, tableName)) {
            String externalInfo = getExternalTableInfo(connection, schema, tableName);
            ddl.append("\n) ").append(externalInfo).append(";\n");
        } else {
            ddl.append("\n);\n");
        }

        // 生成普通索引（非唯一、非主键）
        generateIndexes(ddl, connection, schema, tableName, primaryKey, uniqueConstraints);
        // 添加注释
        appendComments(ddl, schema, tableName, tableComment, columnComments);
        return ddl.toString();
    }

    // 新增注释获取方法
    private static Map<String, String> getComments(Connection conn, String schema, String table) throws SQLException {
        Map<String, String> comments = new HashMap<>();

        String sql = "SELECT c.name AS column_name, ep.value " +
                "FROM sys.extended_properties ep " +
                "JOIN sys.tables t ON ep.major_id = t.object_id " +
                "JOIN sys.columns c ON ep.major_id = c.object_id AND ep.minor_id = c.column_id " +
                "WHERE ep.class = 1 " +
                "AND t.name = ? " +
                "AND SCHEMA_NAME(t.schema_id) = ?";
        log.info("getComments sql:{}", sql);
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, table);
            stmt.setString(2, schema);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                comments.put(rs.getString("column_name"), rs.getString("value"));
            }
        }
        return comments;
    }

    private static Map<String, List<String>> getPrimaryKeyInfo(Connection conn, String schema, String table) throws SQLException {
        Map<String, List<String>> pkInfo = new LinkedHashMap<>();
        String sql = "SELECT i.name AS pk_name, c.name AS column_name " +
                "FROM sys.indexes i " +
                "JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id " +
                "JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id " +
                "WHERE i.is_primary_key = 1 AND OBJECT_NAME(i.object_id) = ?";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, table);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                String pkName = rs.getString("pk_name");
                String column = rs.getString("column_name");
                pkInfo.computeIfAbsent(pkName, k -> new ArrayList<>()).add("[" + column + "]");
            }
        }
        return pkInfo;
    }

    private static Map<String, List<String>> getUniqueConstraints(Connection conn, String schema, String table) throws SQLException {
        Map<String, List<String>> constraints = new LinkedHashMap<>();
        String sql = "SELECT i.name AS constraint_name, c.name AS column_name " +
                "FROM sys.indexes i " +
                "JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id " +
                "JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id " +
                "WHERE i.is_unique = 1 AND i.is_primary_key = 0 AND OBJECT_NAME(i.object_id) = ?";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, table);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                String constrName = rs.getString("constraint_name");
                String column = rs.getString("column_name");
                constraints.computeIfAbsent(constrName, k -> new ArrayList<>()).add("[" + column + "]");
            }
        }
        return constraints;
    }

    private static String buildColumnDefinition(ResultSet column, Map<String, List<String>> primaryKey, Map<String, String> columnComments) throws SQLException {
        StringBuilder colDef = new StringBuilder();
        String columnName = column.getString("COLUMN_NAME");
        String typeName = column.getString("TYPE_NAME");
        int columnSize = column.getInt("COLUMN_SIZE");
        int nullable = column.getInt("NULLABLE");
        boolean isIdentity = "YES".equals(column.getString("IS_AUTOINCREMENT"));

        // 新增默认值处理
        String defaultValue = processDefaultValue(column.getString("COLUMN_DEF"));

        // 新增注释处理
        String comment = columnComments.getOrDefault(columnName, "");

        // 列名
        colDef.append("  [").append(columnName).append("] ");

        // 数据类型处理
        if (typeName.equalsIgnoreCase("varchar") || typeName.equalsIgnoreCase("nvarchar")) {
            colDef.append(typeName.toUpperCase());
            if (columnSize == 2147483647 || columnSize == -1) {
                colDef.append("(MAX)");
            } else if (columnSize > 0) {
                colDef.append("(").append(columnSize).append(")");
            }
        } else if (typeName.equalsIgnoreCase("decimal")) {
            colDef.append("DECIMAL(")
                    .append(column.getInt("COLUMN_SIZE"))
                    .append(",").append(column.getInt("DECIMAL_DIGITS"))
                    .append(")");
        } else {
            colDef.append(typeName);
        }

        // IDENTITY属性
        if (isIdentity) {
            colDef.append(" IDENTITY(1,1)");
        }

        // NULL/NOT NULL（主键列自动NOT NULL）
        boolean isPKColumn = primaryKey.values().stream()
                .anyMatch(list -> list.contains("[" + columnName + "]"));
        if (isPKColumn || nullable == DatabaseMetaData.columnNoNulls) {
            colDef.append(" NOT NULL");
        }

        // 添加默认值
        if (defaultValue != null && !defaultValue.isEmpty()) {
            colDef.append(" DEFAULT ").append(defaultValue);
        }

        // 注释信息暂存（后续统一生成）
        columnComments.put(columnName, comment);

        return colDef.toString();
    }

    private static String processDefaultValue(String rawValue) {
        if (rawValue == null || rawValue.isEmpty()) {
            return null;
        }

        // 处理 SQL Server 默认值格式的嵌套括号（最多处理三层）
        String value = rawValue.trim()
                .replaceAll("^\\{1,3}", "")   // 去除开头1-3个左括号
                .replaceAll("}{1,3}$", ""); // 去除结尾1-3个右括号

        // 处理需要保留括号的特殊情况（如 CHECK 约束表达式）
        if (value.matches("(?i).*\\b(constraint|check|with|for)\\b.*")) {
            return value;
        }

        // 处理函数类默认值（保留原始格式）
        if (value.matches("(?i)^(newid|getdate|next\\s+value\\s+for|abs|convert)\\b.*")) {
            return value;
        }

        // 处理带 Unicode 前缀的字符串（N'value' → 'value'）
        if (value.matches("(?i)^N?'.*'$")) {
            return value.replaceFirst("^N?'(.*)'$", "'$1'")
                    .replace("''", "'"); // 还原转义单引号
        }

        // 处理数值型默认值（确保无多余符号）
        if (value.matches("^[+-]?\\d+(\\.\\d+)?$")) {
            return value.replaceAll("^\\+", ""); // 去除正数前的+号
        }

        return value;
    }

    private static boolean isExternalTable(Connection conn, String schema, String table) throws SQLException {
        String sql = "SELECT is_external FROM sys.tables WHERE name = ? AND schema_id = SCHEMA_ID(?)";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, table);
            stmt.setString(2, schema);
            ResultSet rs = stmt.executeQuery();
            return rs.next() && rs.getBoolean(1);
        }
    }

    private static String getTableComment(Connection conn, String schema, String table) throws SQLException {
        String sql = "SELECT ep.value " +
                "FROM sys.extended_properties ep " +
                "JOIN sys.tables t ON ep.major_id = t.object_id " +
                "WHERE ep.class = 1 " +
                "AND ep.minor_id = 0 " +  // minor_id=0 表示表注释
                "AND t.name = ? " +
                "AND SCHEMA_NAME(t.schema_id) = ?";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, table);
            stmt.setString(2, schema);
            ResultSet rs = stmt.executeQuery();
            return rs.next() ? rs.getString(1) : "";
        }
    }

    private static void appendComments(StringBuilder ddl, String schema, String table,
                                       String tableComment, Map<String, String> columnComments) {
        // 表注释
        if (!tableComment.isEmpty()) {
            ddl.append("\nEXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'")
                    .append(escapeSqlComment(tableComment))
                    .append("', @level0type=N'SCHEMA',@level0name=N'").append(schema)
                    .append("', @level1type=N'TABLE',@level1name=N'").append(table)
                    .append("';\n");
        }

        // 列注释
        for (Map.Entry<String, String> entry : columnComments.entrySet()) {
            String comment = entry.getValue();
            if (!comment.isEmpty()) {
                ddl.append("EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'")
                        .append(escapeSqlComment(comment))
                        .append("', @level0type=N'SCHEMA',@level0name=N'").append(schema)
                        .append("', @level1type=N'TABLE',@level1name=N'").append(table)
                        .append("', @level2type=N'COLUMN',@level2name=N'").append(entry.getKey())
                        .append("';\n");
            }
        }
    }

    private static String escapeSqlComment(String comment) {
        return comment.replace("'", "''");
    }

    private static String getExternalTableInfo(Connection conn, String schema, String table) throws SQLException {
        String sql = "SELECT et.location, eds.name AS data_source, eff.name AS file_format " +
                "FROM sys.external_tables et " +
                "LEFT JOIN sys.external_data_sources eds ON eds.data_source_id = et.data_source_id " +
                "LEFT JOIN sys.external_file_formats eff ON eff.file_format_id = et.file_format_id " +
                "WHERE et.object_id = OBJECT_ID(?);";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, schema + "." + table);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return String.format("WITH (LOCATION = N'%s', DATA_SOURCE = %s, FILE_FORMAT = %s)",
                        rs.getString("location"),
                        rs.getString("data_source"),
                        rs.getString("file_format"));
            }
        }
        return "";
    }

    private static void generateIndexes(StringBuilder ddl, Connection conn, String schema, String table,
                                        Map<String, List<String>> primaryKey,
                                        Map<String, List<String>> uniqueConstraints) throws SQLException {
        String sql = "SELECT i.name AS index_name, c.name AS column_name, i.is_unique " +
                "FROM sys.indexes i " +
                "JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id " +
                "JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id " +
                "WHERE i.type > 0 AND i.is_primary_key = 0 AND i.is_unique_constraint = 0 " +
                "AND OBJECT_NAME(i.object_id) = ?";

        Map<String, List<String>> indexes = new LinkedHashMap<>();
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, table);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                String indexName = rs.getString("index_name");
                if (primaryKey.containsKey(indexName) || uniqueConstraints.containsKey(indexName)) continue;

                indexes.computeIfAbsent(indexName, k -> new ArrayList<>())
                        .add("[" + rs.getString("column_name") + "]");
            }
        }

        for (Map.Entry<String, List<String>> entry : indexes.entrySet()) {
            ddl.append("CREATE ")
                    .append(entry.getValue().size() > 1 ? "INDEX" : "NONCLUSTERED INDEX")
                    .append(" [").append(entry.getKey()).append("] ON [")
                    .append(schema).append("].[").append(table).append("] (")
                    .append(String.join(", ", entry.getValue())).append(");\n");
        }
    }
}
