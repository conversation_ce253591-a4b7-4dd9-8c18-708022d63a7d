/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.sqlserver2017;

import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.utils.CollectionUtil;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.DsIndexDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Sqlserver2017SourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 15:30 2020/1/7
 * @Description：SqlServer 客户端
 */
@Slf4j
public class SqlServerClient extends AbsRdbmsClient {
    // 模糊查询数据库
    private static final String SHOW_DB_LIKE = "select * from master.dbo.sysdatabases where name like '%s'";

    private static final String TABLE_QUERY_ALL = "select * from sys.objects o JOIN sys.schemas s ON o.schema_id = s.schema_id where type='U' or type='V'";
    private static final String TABLE_QUERY = "select * from sys.objects o JOIN sys.schemas s ON o.schema_id = s.schema_id where type='U'";
    private static final String GET_SQLSERVER_CHARACTER_INFO = " SELECT collation_name  FROM sys.databases where name = '%s' ";

    private static final String SQL_SERVER_TABLE_ROW = "SELECT p.rows AS [Row Count]  FROM   sys.partitions p INNER JOIN sys.indexes i ON p.object_id = i.object_id AND p.index_id = i.index_id\n" +
            "WHERE  Object_schema_name(p.object_id) = '%s' AND Object_name(p.object_id) = '%s'";
    private static String SQL_SERVER_COLUMN_NAME = "column_name";
    private static String SQL_SERVER_COLUMN_COMMENT = "column_description";
    private static final String SCHEMAS_QUERY = "select distinct(sys.schemas.name) as schema_name from sys.objects,sys.schemas where sys.objects.type='U' and sys.objects.schema_id=sys.schemas.schema_id";
    private static final String COMMENT_QUERY = "SELECT B.name AS column_name, C.value AS column_description FROM sys.tables A INNER JOIN sys.columns B ON B.object_id = A.object_id LEFT JOIN sys.extended_properties C ON C.major_id = B.object_id AND C.minor_id = B.column_id WHERE A.name = N";
    // 获取正在使用数据库
    private static final String CURRENT_DB = "Select Name From Master..SysDataBases Where DbId=(Select Dbid From Master..SysProcesses Where Spid = @@spid)";
    /**
     * 根据schema获取对应的表：开启cdc的表
     */
    private static final String TABLE_BY_SCHEMA = "SELECT sys.tables.name AS table_name,sys.schemas.name AS schema_name \n" +
            "FROM sys.tables LEFT JOIN sys.schemas ON sys.tables.schema_id=sys.schemas.schema_id \n" +
            "WHERE sys.tables.type='U' AND sys.tables.is_tracked_by_cdc =1\n" +
            "AND sys.schemas.name = '%s'";

    // 获取当前版本号
    private static final String SHOW_VERSION = "SELECT @@VERSION";

    private static final String JDBC_URL = "**************************************";

    private static final String DONT_EXIST = "doesn't exist";
    private static final String SCHEMA_SQL = " and s.name='%s'";

    /**
     * 获取最大连接数
     * 0 = 无限制
     */
    private static final String GET_MAX_CONNECTIONS = "SELECT value_in_use AS max_connections FROM sys.configurations WHERE name = 'user connections'";

    private static final String TABLE_IN_SCHEMA = "select table_name from INFORMATION_SCHEMA.TABLES where  table_schema='%s' and table_name ='%s'";

    private static final Map<Short, String> indexTypeMap = new HashMap<Short, String>() {
        {
            put((short) 0, "tableIndexStatistic");
            put((short) 1, "tableIndexClustered");
            put((short) 2, "tableIndexHashed");
            put((short) 3, "tableIndexOther");
        }
    };

    @Override
    protected ConnFactory getConnFactory() {
        return new SQLServerConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.SQLSERVER_2017_LATER;
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) iSource;
        Integer clearStatus = beforeQuery(sqlserver2017SourceDTO, queryDTO, false);

        Statement statement = null;
        ResultSet rs = null;
        List<String> tableList = new ArrayList<>();
        try {
            String sql = queryDTO.getView() ? TABLE_QUERY_ALL : TABLE_QUERY;
            // 查询schema下的
            if (StringUtils.isNotBlank(queryDTO.getSchema())) {
                sql += String.format(SCHEMA_SQL, queryDTO.getSchema());
            }
            statement = sqlserver2017SourceDTO.getConnection().createStatement();
            DBUtil.setFetchSize(statement, queryDTO);
            rs = statement.executeQuery(sql);
            int columnSize = rs.getMetaData().getColumnCount();
            while (rs.next()) {
                tableList.add(rs.getString(1));
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table exception,%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sqlserver2017SourceDTO, clearStatus));
        }
        return tableList;
    }

    @Override
    public String getCharacterSet(ISourceDTO source, SqlQueryDTO queryDTO) {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) source;
        Integer clearStatus = beforeQuery(sqlserver2017SourceDTO, queryDTO, false);
        String currentDatabase = getCurrentDatabase(source);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = sqlserver2017SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(GET_SQLSERVER_CHARACTER_INFO, currentDatabase));
            while (resultSet.next()) {
                return resultSet.getString(1);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("获取sqlSever系统参数异常，%s",
                    e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserver2017SourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(sqlserver2017SourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        long tableRow = 0L;
        try {
            statement = sqlserver2017SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(SQL_SERVER_TABLE_ROW, queryDTO.getSchema(), queryDTO.getTableName()));
            while (resultSet.next()) {
                tableRow = resultSet.getInt(1);
                return tableRow;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserver2017SourceDTO, clearStatus));
        }
        return tableRow;
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getCharacterSet(source, queryDTO);
    }

    @Override
    public String getCharacterSetByDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getCharacterSet(source, queryDTO);
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name is not empty");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(SHOW_DB_LIKE, dbName)).build()));
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(sqlserver2017SourceDTO, queryDTO);

        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = sqlserver2017SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(
                    "select c.name, cast(isnull(f.[value], '') as nvarchar(100)) as REMARKS\n" +
                            "from sys.objects c " +
                            "left join sys.extended_properties f on f.major_id = c.object_id and f.minor_id = 0 and f.class = 1\n" +
                            "where c.type = 'u'");
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);
                if (dbTableName.equalsIgnoreCase(queryDTO.getTableName())) {
                    return resultSet.getString(DtClassConsistent.PublicConsistent.REMARKS);
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserver2017SourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        Set<ColumnMetaDTO> columns = new HashSet<>();
        List<ColumnMetaDTO> newColumns = new ArrayList<>();
        Statement statement = null;
        ResultSet rs = null;
        //修改 dgr 20220722
        ResultSet rsColumn = null;
        ResultSet pkRs = null;
        ResultSet fkRs = null;
        ResultSet uniqueRs = null;
        ResultSet allIndexRs = null;

        try {
            log.info("------------------开始getColumnMetaData------------------");
            rdbmsSourceDTO.setConnection(getTransConn(rdbmsSourceDTO.getConnection()));
            DatabaseMetaData metaData = rdbmsSourceDTO.getConnection().getMetaData();
            log.info("------------------start------------------");

            String catalog = rdbmsSourceDTO.getConnection().getCatalog();
            if(StringUtils.isNotEmpty(queryDTO.getDbName())){
                catalog=queryDTO.getDbName();
            }
            pkRs = metaData.getPrimaryKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> pkList = new ArrayList<>();
            while (pkRs.next()) {
                pkList.add(pkRs.getString("COLUMN_NAME"));
            }

            log.info("------------------执行pkRs结束------------------");

            fkRs = metaData.getExportedKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> fkList = new ArrayList<>();
            while (fkRs.next()) {
                fkList.add(fkRs.getString("PKCOLUMN_NAME"));
            }

            log.info("------------------执行fkRs结束------------------");

            //oracle视图和oracle表名为小写的时候不支持查询索引
            ArrayList<String> uniqueList = new ArrayList<>();
            ArrayList<DsIndexDTO> allIndexList = new ArrayList<>();
            /*if ((!(rdbmsSourceDTO instanceof OracleSourceDTO) ||
                    (Arrays.stream(queryDTO.getTableTypes()).noneMatch("VIEW"::equalsIgnoreCase))
                            && !queryDTO.getTableName().matches(".*[a-z].*"))) {
                log.info("------------------单独执行uniqueRs  start ------------------");
                uniqueRs = metaData.getIndexInfo(rdbmsSourceDTO.getConnection().getCatalog(), rdbmsSourceDTO.getSchema(), queryDTO.getTableName(), true, false);
                uniqueRs.getStatement().setMaxRows(1);
                log.info("------------------单独执行uniqueRs  end ------------------");
                while (uniqueRs.next()) {
                    uniqueList.add(uniqueRs.getString("COLUMN_NAME"));
                }
                allIndexRs = metaData.getIndexInfo(rdbmsSourceDTO.getConnection().getCatalog(), rdbmsSourceDTO.getSchema(), queryDTO.getTableName(), false, false);
                log.info("------------------单独执行allIndexRs------------------");
                while (allIndexRs.next()) {
                    DsIndexDTO dsIndexDTO = new DsIndexDTO();
                    dsIndexDTO.setColumnName(allIndexRs.getString("COLUMN_NAME"));
                    dsIndexDTO.setUnique(allIndexRs.getBoolean("NON_UNIQUE"));
                    dsIndexDTO.setType(allIndexRs.getShort("TYPE"));
                    allIndexList.add(dsIndexDTO);
                }
            }

            log.info("------------------执行oracle视图和oracle表名为小写的时候不支持查询索引------------------");*/

            rsColumn = metaData.getColumns(catalog, queryDTO.getSchema(), queryDTO.getTableName(), null);
            while (rsColumn.next()) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setPart(false);
                columnMetaDTO.setKey(rsColumn.getString("COLUMN_NAME"));
                columnMetaDTO.setType(rsColumn.getString("TYPE_NAME"));
                columnMetaDTO.setComment(rsColumn.getString("REMARKS"));
                columnMetaDTO.setScale(rsColumn.getInt("DECIMAL_DIGITS"));
                columnMetaDTO.setLength(rsColumn.getInt("COLUMN_SIZE"));
                columnMetaDTO.setDataType(rsColumn.getInt("DATA_TYPE"));
                columnMetaDTO.setDefaultValue(rsColumn.getString("COLUMN_DEF"));
                columnMetaDTO.setNotNullFlag("no".equals(rsColumn.getString("IS_NULLABLE").toLowerCase()));
                if (pkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setPkflag(true);
                } else {
                    columnMetaDTO.setPkflag(false);
                }
                if (fkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setFkflag(true);
                } else {
                    columnMetaDTO.setFkflag(false);
                }
                if (uniqueList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setUniqueFlag(true);
                } else {
                    columnMetaDTO.setUniqueFlag(false);
                }

                for (DsIndexDTO dsIndexDTO : allIndexList
                ) {
                    if (rsColumn.getString("COLUMN_NAME").equals(dsIndexDTO.getColumnName())) {
                        columnMetaDTO.setIndexType(indexTypeMap.get(dsIndexDTO.getType()));
                    }
                }

                columns.add(columnMetaDTO);
            }

            log.info("------------------执行getColumns结束------------------");

            statement = rdbmsSourceDTO.getConnection().createStatement();
            statement.setMaxRows(1);
            String queryColumnSql =
                    "select " + CollectionUtil.listToStr(queryDTO.getColumns()) + " from " +catalog+"." + transferSchemaAndTableName(rdbmsSourceDTO, queryDTO) + " where 1=2";

            rs = statement.executeQuery(queryColumnSql);

            log.info("------------------queryColumnSql{}------------------",queryColumnSql);
            log.info("------------------执行select结束------------------");

            ResultSetMetaData rsMetaData = rs.getMetaData();
            int columnCount = rsMetaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMetaData.getColumnName(i);
                for (ColumnMetaDTO columnMetaDTO : columns) {
                    if (columnMetaDTO.getKey().equals(columnName)) {
                        columnMetaDTO.setPrecision(rsMetaData.getPrecision(i));
                        columnMetaDTO.setDateType(rsMetaData.getColumnClassName(i));
                        newColumns.add(columnMetaDTO);
                    }
                }
            }
            Map<String, String> columnComments = getColumnComments((RdbmsSourceDTO) iSource, queryDTO);
            for (ColumnMetaDTO columnMetaDatum : newColumns) {
                columnMetaDatum.setComment(columnComments.get(columnMetaDatum.getKey()));
            }

        } catch (SQLException e) {
            log.error("------------------表{}，字段采集报错{}------------------",queryDTO.getTableName(),e.getMessage());
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the meta information of the fields of the table: %s. Please contact the DBA to check the database and table information: %s",
                        queryDTO.getTableName(), e.getMessage()), e);
            }
        } finally {
            DBUtil.closeDBResources(pkRs, null, null);
            DBUtil.closeDBResources(fkRs, null, null);
            DBUtil.closeDBResources(uniqueRs, null, null);
            DBUtil.closeDBResources(allIndexRs, null, null);
            DBUtil.closeDBResources(rsColumn, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return newColumns;
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        Sqlserver2017SourceDTO sqlserver2017SourceDTO = (Sqlserver2017SourceDTO) source;
        SqlServerDownloader sqlServerDownloader = new SqlServerDownloader(getCon(sqlserver2017SourceDTO), queryDTO.getSql(), sqlserver2017SourceDTO.getSchema());
        sqlServerDownloader.configure();
        return sqlServerDownloader;
    }

    @Override
    public String dealSql(ISourceDTO source, SqlQueryDTO sqlQueryDTO) {
        return "select top " + sqlQueryDTO.getPreviewNum() + " * from " + transferSchemaAndTableName(source, sqlQueryDTO);
    }

    @Override
    protected String getDbSeparator() {
        return "\"";
    }

    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (StringUtils.isBlank(schema)) {
            return transferTableName(tableName);
        }
        if (!tableName.startsWith("[") || !tableName.endsWith("]")) {
            tableName = String.format("[%s]", tableName);
        }
        if (!schema.startsWith("[") || !schema.endsWith("]")) {
            schema = String.format("[%s]", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    @Override
    protected String transferTableName(String tableName) {
        //如果传过来是[tableName]格式直接当成表名
        if (tableName.startsWith("[") && tableName.endsWith("]")) {
            return tableName;
        }
        //如果不是上述格式，判断有没有"."符号，有的话，第一个"."之前的当成schema，后面的当成表名进行[tableName]处理
        if (tableName.contains(".")) {
            //切割，表名中可能会有包含"."的情况，所以限制切割后长度为2
            String[] tables = tableName.split("\\.", 2);
            tableName = tables[1];
            return String.format("%s.%s", tables[0], tableName.contains("[") ? tableName : String.format("[%s]",
                    tableName));
        }
        //判断表名
        return String.format("[%s]", tableName);
    }

    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return String.format(TABLE_BY_SCHEMA, queryDTO.getSchema());
    }

    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        String result = null;
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) source;
        Integer clearStatus = beforeQuery(rdbmsSourceDTO, queryDTO, false);

        if(SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())|| SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())){
            String sql=String.format(SqlConstants.GET_F_P_CREATE_SQL,queryDTO.getSchema(),queryDTO.getTableName());
            log.error("获取函数或者存储过程的ddl:{}",sql);
            Statement statement = null;
            ResultSet rs = null;
            String createTableSql = null;
            try {
                statement = rdbmsSourceDTO.getConnection().createStatement();
                rs = statement.executeQuery(sql);
                int columnSize = rs.getMetaData().getColumnCount();
                while (rs.next()) {
                    createTableSql = rs.getString(columnSize == 1 ? 1 : 2);
                    break;
                }
            } catch (Exception e) {
                throw new DtLoaderException(String.format("failed to get the create table sql：%s", e.getMessage()), e);
            } finally {
                DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            }
            return createTableSql;
        }

        try (Connection connection = rdbmsSourceDTO.getConnection()){
            String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : rdbmsSourceDTO.getSchema();
            result = SqlServerDDLGenerator.generateTableDDL(connection, schema, queryDTO.getTableName());
        } catch (Exception e) {
            throw new DtLoaderException(String.format("failed to get the create table sql：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return result;
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        Statement statement = null;
        ResultSet rs = null;
        Map<String, String> columnComments = new HashMap<>();
        try {
            statement = sourceDTO.getConnection().createStatement();
            String queryColumnCommentSql = COMMENT_QUERY + addSingleQuotes(queryDTO.getTableName());
            rs = statement.executeQuery(queryColumnCommentSql);
            while (rs.next()) {
                String columnName = rs.getString(SQL_SERVER_COLUMN_NAME);
                String columnComment = rs.getString(SQL_SERVER_COLUMN_COMMENT);
                columnComments.put(columnName, columnComment);
            }

        } catch (Exception e) {
            //获取表字段注释失败
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sourceDTO, clearStatus));
        }
        return columnComments;
    }

    private static String addSingleQuotes(String str) {
        str = str.contains("'") ? str : String.format("'%s'", str);
        return str;
    }

    @Override
    public String getShowDbSql() {
        return SqlConstants.ALL_SCHEMA;
    }

    /**
     * 获取指定库下的schema
     * @return
     */
    @Override
    protected String getDbsSql() {
        return SqlConstants.ALL_DATABASE;
    }
    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    public String getVersionSql() {
        return SHOW_VERSION;
    }
    protected String getJdbcUrl( DatasourceInfoImportVO datasourceInfoImportVO) {
        String jdbcUrl = String.format(JDBC_URL, datasourceInfoImportVO.getIp(), datasourceInfoImportVO.getPort(),datasourceInfoImportVO.getDbName());
        return jdbcUrl;
    }

    @Override
    public Integer getMaxConnections(ISourceDTO sourceDTO) {
        Integer result = null;
        try {
            List<Map<String, Object>> resultList = executeQuery(sourceDTO, SqlQueryDTO.builder().sql(GET_MAX_CONNECTIONS).build());
            if (CollectionUtils.isNotEmpty(resultList)) {
                result = MapUtils.getInteger(resultList.get(0), "max_connections");
            }
        } catch (Exception e) {
            log.error("get max connections error", e);
        }
        return result;
    }
    /**
     * 获取表
     * @param sourceDTO 数据源描述对象，包含连接数据库所需的信息，如数据库类型、连接字符串等
     * @param queryDTO  SQL查询对象，包含查询数据库表所需的条件，如表名、schema名等
     * @return
     */
    @Override
    public List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Sqlserver2017SourceDTO sqlserverSourceDTO = (Sqlserver2017SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> dbTableVOS = new ArrayList<>();
        try {
            statement = sqlserverSourceDTO.getConnection().createStatement();

            //判断搜查类型为空时查全部
            String type = SqlConstants.BASE_TABLE;
            String sql = "";
            if (SqlConstants.VIEW_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                type = SqlConstants.VIEW;
            }
            sql = String.format(SqlConstants.GET_TABLE_SCHEMA_SQL, queryDTO.getSchema(), type);

            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            log.info("getMedataDataTables SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO dbTableVO = new DbTableVO();
                dbTableVO.setDbName(queryDTO.getDbName());
                dbTableVO.setSchemaName(resultSet.getString("TABLE_SCHEMA"));
                dbTableVO.setName(resultSet.getString("TABLE_NAME"));
                dbTableVO.setType(queryDTO.getType());
                dbTableVOS.add(dbTableVO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取表或者视图异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return dbTableVOS;
    }

    /**
     * 获取索引信息
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    @Override
    public  List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        Sqlserver2017SourceDTO sqlserverSourceDTO = (Sqlserver2017SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = sqlserverSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = String.format(SqlConstants.GET_INDEX_SQL, queryDTO.getSchema());
            //是否有模糊搜索
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_INDEX_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            //拼上分组
            sql=sql+SqlConstants.GROUP_INDEX_SQL;
            log.info("getIndexList SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setSchemaName(resultSet.getString("schema_name"));
                indexMetaDTO.setTableName(resultSet.getString("TABLE_NAME"));
                indexMetaDTO.setName(resultSet.getString("INDEX_NAME"));
                String indexType = resultSet.getString("INDEX_TYPE");
                indexMetaDTO.setIndexType(indexType);
                indexMetaDTO.setType(queryDTO.getType());
                int isUnique = resultSet.getInt("is_unique");
                if(1==isUnique){
                    indexMetaDTO.setUnique(0);
                }
                if(0==isUnique){
                    indexMetaDTO.setUnique(1);
                }
                indexMetaDTO.setDbName(queryDTO.getDbName());
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public   List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        Sqlserver2017SourceDTO sqlserverSourceDTO = (Sqlserver2017SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = sqlserverSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String  sql = String.format(SqlConstants.GET_INDEX_COLUMN_SQL, queryDTO.getSchema(), queryDTO.getTableName(),queryDTO.getIndexName());
            log.info("getIndexColumn SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                indexMetaDTO.setKey(resultSet.getString("COLUMN_NAME"));
                indexMetaDTO.setColumnOrder(resultSet.getInt("SEQ_IN_INDEX"));
                int collation = resultSet.getInt("collation");
                if (0 == collation) {
                    indexMetaDTO.setCollation("asc");
                } else if (1 == collation) {
                    indexMetaDTO.setCollation("desc");
                } else {
                    indexMetaDTO.setCollation("");
                }
                int nullable = resultSet.getInt("NULLABLE");
                //如果是yes  则可为空
                if (0==nullable) {
                    indexMetaDTO.setNotNullFlag(true);
                } else {
                    indexMetaDTO.setNotNullFlag(false);
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引字段异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return indexCols;
    }
    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        Sqlserver2017SourceDTO sqlserverSourceDTO = (Sqlserver2017SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = sqlserverSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //type 为函数还是存储过程
            if(SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())){
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getDbName(),SqlConstants.FUNCTION_TYPE,SqlConstants.FUNCTION_TYPE);
            }

            if(SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())){
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getSchema(),SqlConstants.PROCEDURE_TYPE,SqlConstants.PROCEDURE_TYPE);
            }
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_PRODUCE_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }

            log.info("getFunctionList SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(queryDTO.getDbName());
                indexMetaDTO.setSchemaName(resultSet.getString("ROUTINE_SCHEMA"));
                indexMetaDTO.setName(resultSet.getString("ROUTINE_NAME"));
                String indexType = resultSet.getString("ROUTINE_TYPE");
                indexMetaDTO.setType(indexType);
                indexMetaDTO.setDdl(resultSet.getString("ROUTINE_DEFINITION"));
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        Sqlserver2017SourceDTO sqlserverSourceDTO = (Sqlserver2017SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = sqlserverSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String  sql = String.format(SqlConstants.GET_PRODUCE_ARGUMENTS_SQL, queryDTO.getSchema(),queryDTO.getObjectName()).toLowerCase();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                String parameterName = resultSet.getString("PARAMETER_NAME");
                if(StringUtils.isEmpty(parameterName)){
                    parameterName="RETURN";
                }
                indexMetaDTO.setKey(parameterName);
                indexMetaDTO.setType(resultSet.getString("DATA_TYPE"));
                String parameterMode = resultSet.getString("PARAMETER_MODE");
                if("1".equalsIgnoreCase(parameterMode)){
                    parameterMode="OUT";
                }else{
                    parameterMode="IN";
                }
                indexMetaDTO.setInOut(parameterMode);
                indexMetaDTO.setColumnOrder(resultSet.getInt("ORDINAL_POSITION"));
                int characterOctetLength = resultSet.getInt("CHARACTER_OCTET_LENGTH");
                int numericPrecision = resultSet.getInt("NUMERIC_PRECISION");
                if(characterOctetLength>0){
                    indexMetaDTO.setLength(characterOctetLength);
                }else{
                    indexMetaDTO.setLength(numericPrecision);
                    indexMetaDTO.setPrecision(numericPrecision);
                    indexMetaDTO.setScale(resultSet.getInt("NUMERIC_SCALE"));
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return indexCols;
    }

    @Override
    public Boolean getMetadataPrivileges(ISourceDTO sourceDTO) {
        Boolean result = null;
        Sqlserver2017SourceDTO sqlserverSourceDTO = (Sqlserver2017SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, SqlQueryDTO.builder().build(), false);
        try {
            return isSysAdmin(sqlserverSourceDTO.getConnection())
                    || hasViewAnyDefinition(sqlserverSourceDTO.getConnection())
                    || testMetadataQuery(sqlserverSourceDTO.getConnection());
        } catch (Exception e) {
            // 异常为无权限
            result = false;
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return result;
    }

    private boolean isSysAdmin(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT IS_SRVROLEMEMBER('sysadmin') AS is_sysadmin")) {
            return rs.next() && rs.getInt("is_sysadmin") == 1;
        }
    }

    private boolean hasViewAnyDefinition(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT HAS_PERMS_BY_NAME(null, null, 'VIEW ANY DEFINITION') AS has_perm")) {
            return rs.next() && rs.getInt("has_perm") == 1;
        }
    }

    private boolean testMetadataQuery(Connection conn) {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT TOP 1 name FROM sys.tables")) {
            return rs.next(); // 成功查询则返回 true
        } catch (SQLException e) {
            return false; // 权限不足时返回 false
        }
    }
    @Override
    public String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {
        String sql = String.format(TABLE_IN_SCHEMA, queryDTO.getSchema(), queryDTO.getTableName());
        return sql;
    }
}
