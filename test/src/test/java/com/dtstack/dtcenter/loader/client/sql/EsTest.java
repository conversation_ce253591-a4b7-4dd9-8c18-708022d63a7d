/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.sql;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.client.BaseTest;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.Table;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.ESSourceDTO;
import com.dtstack.dtcenter.loader.enums.EsCommandType;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 23:09 2020/2/28
 * @Description：ES 测试
 */
public class EsTest extends BaseTest {

    private static final IClient client = ClientCache.getClient(DataSourceType.ES6.getVal());

    private static final ESSourceDTO source = ESSourceDTO.builder()
            .url("***************:9200")
            .username("dedp")
            .password("Cdyanfa_123456")
            .poolConfig(new PoolConfig())
            .build();

    /**
     * 用户名和密码不正确，**************:9200 不需要密码
     */
    private static final ESSourceDTO esSource = ESSourceDTO.builder()
            .url("**************:9200")
            .username("dedp")
            .password("Cdyanfa_123456")
            .build();

    /**
     * 数据准备
     */
//    @BeforeClass
    public static void setUp () {
        String sql = "{\"name\": \"小黄\", \"age\": 18,\"sex\": \"不详\",\"extraAttr_0_5_3\":{\"attributeValue\":\"2020-09-17 23:37:16\"}}";
        String tableName = "/test/_doc/1";
        client.executeSqlWithoutResultSet(esSource, SqlQueryDTO.builder().sql(sql).tableName(tableName).esCommandType(EsCommandType.INSERT.getType()).build());
    }

    @Test
    public void testCon() throws Exception {

//        DatasourceDTO datasourceDTO = DatasourceDTO.builder()
//                .dataJson("eyJwYXNzd29yZCI6ImNoYW5nZW1lIiwiZGF0YVZlcnNpb24iOiI1LngiLCJkYXRhVHlwZUNvZGUiOjExLCJkYXRhVHlwZSI6IkVsYXN0aWNzZWFyY2giLCJkcml2ZXJDbGFzc05hbWUiOiIiLCJzaG93RGF0YVR5cGUiOiJFbGFzdGljc2VhcmNoIiwiZGF0YU5hbWUiOiJzaF9lczUiLCJidXNpbmVzc1V1aWQiOiI0NDFlYWRmNjg1YWE0NTE0YjY0NTExNGRmZDFhOTc3NyIsInVybCI6IjE5Mi4xNjguMjMuNTc6OTIwMCIsInVzZXJuYW1lIjoiZWxhc3RpYyIsInNjaGVtYSI6IiIsImRiVmVyc2lvbiI6bnVsbH0=")
//                .dataType("Elasticsearch")
//                .dataVersion("5.x")
//                .build();
//        Boolean aBoolean = DatasourceUtils.checkConnectionWithConf(datasourceDTO, null, null);
//        System.out.println(aBoolean);


        Boolean isConnected = client.testCon(esSource);
        if (Boolean.FALSE.equals(isConnected)) {
            throw new DtLoaderException("connection exception");
        }
    }

    @Test
    public void getVersion() throws Exception {
        String version = client.getVersion(esSource);
        System.out.println(version);
    }

    @Test
    public void getAllDb () {
        List databases = client.getAllDatabases(esSource, SqlQueryDTO.builder().build());
        Assert.assertTrue(CollectionUtils.isNotEmpty(databases));
    }

    @Test
    public void createDatabaset () {

        Boolean test = client.createDatabase(esSource, "test01", "");
        System.out.println(test);

    }

    @Test
    public void getTableList() {
        List tableList = client.getTableList(esSource, SqlQueryDTO.builder().schema("e__validate_72c5b9b8a053ba37df60aef7ed101fe3_002_q__log").build());
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
    }

    @Test
    public void getPreview() {
        List viewList = client.getPreview(esSource, SqlQueryDTO.builder().schema("e__validate_72c5b9b8a053ba37df60aef7ed101fe3_002_q__log").previewNum(1).build());
        Assert.assertTrue(CollectionUtils.isNotEmpty(viewList));
    }

    @Test
    public void getColumnMetaData() {
        List metaData = client.getColumnMetaData(esSource, SqlQueryDTO.builder().schema("e__validate_72c5b9b8a053ba37df60aef7ed101fe3_002_q__log").tableName("_doc").build());
        Assert.assertTrue(CollectionUtils.isNotEmpty(metaData));
    }

    @Test
    public void executeQuery() {
        List<Map<String, Object>> list = client.executeQuery(esSource, SqlQueryDTO.builder().sql("{\"query\": {\"match_all\": {} }}").tableName("commodity").build());
        JSONObject result = (JSONObject) list.get(0).get("result");
        Assert.assertNotNull(result);
    }

    /**
     * 删除
     */
    @Test
    public void executeSqlWithoutResultSet() {
        IClient client = ClientCache.getClient(DataSourceType.ES.getVal());
        String tableName = "commodity/_doc/1";
        client.executeSqlWithoutResultSet(source, SqlQueryDTO.builder().tableName(tableName).esCommandType(EsCommandType.DELETE.getType()).build());
    }

    @Test
    public void executeSqlWithoutResultSet4() {
        String sql = "{\"doc\":{\"age\":26 }}";
        String tableName = "commodity/_doc/3";
        client.executeSqlWithoutResultSet(source, SqlQueryDTO.builder().sql(sql).tableName(tableName).esCommandType(EsCommandType.UPDATE.getType()).build());
    }

    /**
     * 插数据测试
     */
    @Test
    public void executeSqlWithoutResultSet3() {
        String sql = "{\"name\": \"小黄\", \"age\": 18,\"sex\": \"不详\"}";
        String tableName = "/commodity/doc1/2";
        client.executeSqlWithoutResultSet(esSource, SqlQueryDTO.builder().sql(sql).tableName(tableName).esCommandType(EsCommandType.INSERT.getType()).build());
    }

    /**
     * 根据查询更新
     */
    @Test
    public void executeSqlWithoutResultSet2() {
        String sql = "{\"query\": {\"match_all\": {} }}";
        String tableName = "commodity/_doc";
        client.executeSqlWithoutResultSet(source, SqlQueryDTO.builder().sql(sql).tableName(tableName).esCommandType(EsCommandType.UPDATE_BY_QUERY.getType()).build());
    }

    /**
     * 根据查询删除
     */
    @Test
    public void executeSqlWithoutResultSet1() {
        String sql = "{\"query\":{\"match_all\": {}}}";
        String tableName = "commodity/_doc";
        client.executeSqlWithoutResultSet(source, SqlQueryDTO.builder().sql(sql).tableName(tableName).esCommandType(EsCommandType.DELETE_BY_QUERY.getType()).build());
    }

    /**
     * 连接失败
     */
    @Test
    public void testConFalse() {
        ESSourceDTO source = new ESSourceDTO();
        Boolean isConnected = client.testCon(source);
        Assert.assertFalse(isConnected);
    }

    /**
     * 获取表失败
     */
    @Test(expected = DtLoaderException.class)
    public void getTableListFalse() {
        ESSourceDTO source1 = new ESSourceDTO();
        List<String> list = client.getTableList(source1, null);
        assert CollectionUtils.isEmpty(list);
        client.getTableList(source, SqlQueryDTO.builder().build());
    }

    /**
     * 获取ES所有索引,校验null
     */
    @Test
    public void getAllDatabasesFalse() {
        ESSourceDTO source1 = new ESSourceDTO();
        List<String> list1= client.getAllDatabases(esSource, null);
//        assert CollectionUtils.isEmpty(list1);
        List commodity = client.getAllDatabases(esSource, SqlQueryDTO.builder().tableName("commodity").build());
        System.out.println(commodity);
    }

    /**
     * 数据预览，检验null
     */
    @Test(expected = DtLoaderException.class)
    public void getPreviewFalse() {
        ESSourceDTO source1 = new ESSourceDTO();
        List<List<Object>> list1= client.getPreview(esSource, SqlQueryDTO.builder().tableName("commodity").build());
        assert CollectionUtils.isEmpty(list1);
        client.getPreview(source, SqlQueryDTO.builder().build());
    }

    /**
     * 获取ES字段信息 ，校验null
     */
    @Test(expected = DtLoaderException.class)
    public void getColumnMetaDataReturnFalse(){
        ESSourceDTO source1 = new ESSourceDTO();
        List<List<Object>> list1= client.getColumnMetaData(esSource,  SqlQueryDTO.builder().tableName("commodity").build());
        assert CollectionUtils.isEmpty(list1);
        client.getColumnMetaData(source, SqlQueryDTO.builder().build());
    }

    /**
     * 执行query，校验null
     */
    @Test(expected = DtLoaderException.class)
    public void executeQueryReturnFalse(){
        ESSourceDTO source1 = new ESSourceDTO();
        List<List<Object>> list1= client.executeQuery(source1, null);
        assert CollectionUtils.isEmpty(list1);
        client.executeQuery(source, SqlQueryDTO.builder().build());
    }

    /**
     * 测试链接，使用用户名密码
     */
    @Test
    public void testConUseName() {
        Boolean isConnected = client.testCon(esSource);
        Assert.assertTrue(isConnected);
        Boolean isConnect = client.testCon(esSource);
        Assert.assertTrue(isConnect);
    }

    @Test(expected = DtLoaderException.class)
    public void getColumnClassInfo() {

        List jnbyTag0913 = client.getColumnClassInfo(esSource, SqlQueryDTO.builder().tableName("jnby_tag_0913").build());
        System.out.println(jnbyTag0913);
    }

    @Test(expected = DtLoaderException.class)
    public void getColumnMetaDataWithSql() {
        List jnbyTag0913 = client.getColumnMetaDataWithSql(esSource, SqlQueryDTO.builder().tableName("jnby_tag_0913").build());
        System.out.println(jnbyTag0913);

    }

    @Test(expected = DtLoaderException.class)
    public void getCreateTableSql() {
        String jnbyTag0913 = client.getCreateTableSql(esSource, SqlQueryDTO.builder().tableName("jnby_tag_0913").build());
        System.out.println(jnbyTag0913);
    }


    @Test(expected = DtLoaderException.class)
    public void getPartitionColumn() {
        client.getPartitionColumn(esSource, SqlQueryDTO.builder().tableName("jnby_tag_0913").build());
    }

    @Test(expected = DtLoaderException.class)
    public void getDownloader() throws Exception {
        IDownloader jnbyTag0913 = client.getDownloader(esSource, SqlQueryDTO.builder().tableName("jnby_tag_0913").build());
        System.out.println(jnbyTag0913);
    }

    @Test(expected = DtLoaderException.class)
    public void getFlinkColumnMetaData() {
        client.getFlinkColumnMetaData(esSource, SqlQueryDTO.builder().tableName("jnby_tag_0913").build());
    }

    @Test(expected = DtLoaderException.class)
    public void getTableMetaComment() {
        String jnbyTag0913 = client.getTableMetaComment(esSource, SqlQueryDTO.builder().tableName("jnby_tag_0913").build());
        System.out.println(jnbyTag0913);
    }

    @Test(expected = DtLoaderException.class)
    public void getTable() {

        Table jnbyTag0913 = client.getTable(esSource, SqlQueryDTO.builder().tableName("jnby_tag_0913").build());
        System.out.println(jnbyTag0913);
    }

    @Test(expected = DtLoaderException.class)
    public void getCurrentDatabase() {
        client.getCurrentDatabase(esSource);
    }

    @Test(expected = DtLoaderException.class)
    public void createDatabase() {
        Boolean test0001 = client.createDatabase(esSource, "test0001", null);
        System.out.println(test0001);
    }

    @Test(expected = DtLoaderException.class)
    public void isDatabaseExists() {
        client.isDatabaseExists(esSource, "test0001");
    }



    @Test(expected = DtLoaderException.class)
    public void isTableExistsInDatabase() {
        SqlQueryDTO  queryDTO = SqlQueryDTO.builder().tableName("my_index<_doc>").build();
        Boolean tableExistsInDatabase = client.isTableExistsInDatabase(source, queryDTO);
        Assert.assertFalse(tableExistsInDatabase);
    }

    @Test()
    public void getIndexAndTable() {

        DatasourceDTO datasourceDTO = DatasourceDTO.builder()
                .dataJson("***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
                .dataType("Elasticsearch")
                .dataVersion("6.x")
                .tableName("my_index<_doc>")
                .dataTypeCode(DataSourceType.ES6.getVal())
                .build();
        Boolean tableExistsInDatabase = DatasourceUtils.isTableExistsInDatabase(datasourceDTO);
        System.out.println(tableExistsInDatabase);

    }

    @Test
    public void testCol() {
        String dataType="double(12,2)";
        Pattern columnPattern = Pattern.compile("(\\w+)\\((\\d+)(?:,\\s*(\\d+))?\\)");

        Matcher matcher = columnPattern.matcher(dataType);
        if (matcher.find()) {
            String type = matcher.group(1);
            String precision = matcher.group(2);
            String scale = matcher.group(3);
            if(StringUtils.isNotEmpty(type)){
                System.out.println(type);
            }
            if(StringUtils.isNotEmpty(precision)){
                System.out.println(precision);
            }
            if(StringUtils.isNotEmpty(scale)){
                System.out.println(scale);
            }
        }
    }

}
