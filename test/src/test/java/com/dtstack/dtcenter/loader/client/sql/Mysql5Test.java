/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.sql;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.dto.DatasourceInfoDTO;
import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.dtstack.dtcenter.common.loader.mysql5.MysqlClient;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.client.BaseTest;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.Mysql5SourceDTO;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 03:41 2020/2/29
 * @Description：MySQL 5 测试
 */
public class Mysql5Test extends BaseTest {

    // 获取数据源 client
    private static final IClient client = ClientCache.getClient(DataSourceType.MySQL.getVal());

    // 构建数据源信息
    private static final Mysql5SourceDTO source = Mysql5SourceDTO.builder()
            .url("******************************************")
//            .url("**************************************************")
            .username("root")
            .password("Cdyanfa_123456")
//            .password("joyadata")
            .poolConfig(PoolConfig.builder().build())
            .build();

    /**
     * 数据预处理
     */
//    @BeforeClass
    public static void beforeClass() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("drop table if exists LOADER_TEST").build();
        client.executeSqlWithoutResultSet(source, queryDTO);
        queryDTO = SqlQueryDTO.builder().sql("create table LOADER_TEST (id int COMMENT 'id', name varchar(50) COMMENT '姓名') comment 'table comment'").build();
        client.executeSqlWithoutResultSet(source, queryDTO);
        queryDTO = SqlQueryDTO.builder().sql("insert into LOADER_TEST values (1, 'LOADER_TEST')").build();
        client.executeSqlWithoutResultSet(source, queryDTO);
        queryDTO = SqlQueryDTO.builder().sql("drop view if exists LOADER_TEST_VIEW").build();
        client.executeSqlWithoutResultSet(source, queryDTO);
        queryDTO = SqlQueryDTO.builder().sql("create view LOADER_TEST_VIEW as select * from LOADER_TEST").build();
        client.executeSqlWithoutResultSet(source, queryDTO);
    }

    /**
     * 根据schema获取表
     */
    @Test
    public void getTableListBySchema_001() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().schema("test").build();
        List<String> tableList = client.getTableListBySchema(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
    }

    /**
     * 选择schema
     */
    @Test
    public void getTableList_0001() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        List<String> tableList = client.getTableList(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
    }

    @Test
    public void getColumnMetaData_0001() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().schema("bj").tableName("a002").build();
        List<ColumnMetaDTO> columnMetaData = client.getColumnMetaData(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(columnMetaData));
    }


    /**
     * 获取连接测试
     */
    @Test
    public void getCon() throws Exception {
        Connection connection = client.getCon(source);
        Assert.assertNotNull(connection);
        connection.close();
    }

    /**
     * 连通性测试
     */
    @Test
    public void testCon() {
        Boolean isConnected = client.testCon(source);
        Assert.assertTrue(isConnected);
    }


    /**
     * 字段名称重复测试
     */
    @Test
    public void executeQueryRepeatColumn() {
        String sql = "select id, name ,id as id, name, name as name, id as name,name as id from LOADER_TEST";
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql(sql).build();
        List<Map<String, Object>> result = client.executeQuery(source, queryDTO);
        Map<String, Object> row = result.get(0);
        Assert.assertEquals(7, row.size());
        Assert.assertTrue(row.containsKey("name") && row.containsKey("name(1)")
                && row.containsKey("name(2)") && row.containsKey("name(3)")
                && row.containsKey("id") && row.containsKey("id(1)")
                && row.containsKey("id(2)"));
    }

    /**
     * 字段别名测试
     */
    @Test
    public void executeQueryAlias() {
        String sql = "select id as testAlias from LOADER_TEST;";
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql(sql).build();
        List<Map<String, Object>> result = client.executeQuery(source, queryDTO);
        Assert.assertTrue(result.get(0).containsKey("testAlias"));
    }

    /**
     * 无需结果查询
     */
    @Test
    public void executeSqlWithoutResultSet() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("use qjq").build();
        client.executeSqlWithoutResultSet(source, queryDTO);
    }


    /**
     * 预编译查询
     */
    @Test
    public void executeQuery() {
        String preSql = "use bj";
        String sql = " EXPLAIN select * from emp_0324";
//        List<Object> preFields = new ArrayList<>();
//        preFields.add(0);
//        preFields.add(5);
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql(sql).preSql(preSql).previewNum(1).build();
        List<Map<String, Object>> result = client.executeQuery(source, queryDTO);
//        List<Map<String, Object>> result = client.executeSqlForMetadataInfo(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    /**
     * 获取表列表
     */
    @Test
    public void getTableList() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        List<String> tableList = client.getTableList(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
    }

    /**
     * 获取表列表
     */
    @Test
    public void getTableAndViewList() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        List<TableViewDTO> tableList = client.getTableAndViewList(source, queryDTO);
        for (TableViewDTO tableViewDTO : tableList) {
            if ("tab_shili".equalsIgnoreCase(tableViewDTO.getName())) {
                System.out.println(tableViewDTO);
            }
        }

        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
    }

    /**
     * 根据schema获取表
     */
    @Test
    public void getTableListBySchema() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().schema("dev").build();
        List<String> tableList = client.getTableListBySchema(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
    }

    /**
     * 获取表字段java标准格式
     */
    @Test
    public void getColumnClassInfo() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("LOADER_TEST").build();
        List<String> columnClassInfo = client.getColumnClassInfo(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(columnClassInfo));
    }

    /**
     * 获取字符集
     */
    @Test
    public void getCharacterSet() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().dbName("metadata").tableName("table_1").build();
        String a = client.getCharacterSet(source, queryDTO);
        System.out.println(a);
        Assert.assertTrue(StringUtils.isNotBlank(a));
    }

    /**
     * 获取排序规则
     */
    @Test
    public void getCharacterCollation() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("catalogue_tree").build();
        String a = client.getCharacterCollation(source, queryDTO);
        System.out.println(a);
        Assert.assertTrue(StringUtils.isNotBlank(a));
    }

    /**
     * 获取表行数
     */
    @Test
    public void getTableRows() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("catalogue_tree").build();
        Long a = client.getTableRows(source, queryDTO);
        System.out.println(a);
        Assert.assertTrue(StringUtils.isNotBlank(a + ""));
    }

    /**
     * 获取表字段信息
     */
    @Test
    public void getColumnMetaData() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("catalogue_tree").build();
        List<ColumnMetaDTO> columnMetaData = client.getColumnMetaData(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(columnMetaData));
    }

    /**
     * 获取表注释
     */
    @Test
    public void getTableMetaComment() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("LOADER_TEST").build();
        String metaComment = client.getTableMetaComment(source, queryDTO);
        Assert.assertTrue(StringUtils.isNotBlank(metaComment));
    }


    /**
     * 自定义sql 数据下载测试
     */
    @Test
    public void testGetDownloader() throws Exception {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("select * from LOADER_TEST").build();
        IDownloader downloader = client.getDownloader(source, queryDTO);
        List<String> metaInfo = downloader.getMetaInfo();
        Assert.assertTrue(CollectionUtils.isNotEmpty(metaInfo));
        while (!downloader.reachedEnd()) {
            List<List<String>> result = (List<List<String>>) downloader.readNext();
            for (List<String> row : result) {
                Assert.assertTrue(CollectionUtils.isNotEmpty(row));
            }
        }
    }

    /**
     * 数据预览测试
     */
    @Test
    public void testGetPreview() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("LOADER_TEST").build();
        List preview = client.getPreview(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(preview));
    }

    /**
     * 根据自定义sql获取表字段信息
     */
    @Test
    public void getColumnMetaDataWithSql() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("select * from LOADER_TEST").build();
        List sql = client.getColumnMetaDataWithSql(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(sql));
    }

    /**
     * 获取所有的db
     */
    @Test
    public void getAllDatabases() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        Assert.assertTrue(CollectionUtils.isNotEmpty(client.getAllDatabases(source, queryDTO)));
    }

    /**
     * 获取表建表语句
     */
    @Test
    public void getCreateTableSql() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().schema("metadata").tableName("table_1").type("table").build();
        String createTableSql = client.getCreateTableSql(source, queryDTO);
        Assert.assertTrue(StringUtils.isNotBlank(createTableSql));
//        DatasourceDTO datasourceInfoDTO = new DatasourceDTO();
//        datasourceInfoDTO.setDataType(DataSourceTypeEnum.MySQL.getDataType());
//        datasourceInfoDTO.setDataVersion(DataSourceTypeEnum.MySQL.getDataVersion());
//        datasourceInfoDTO.setDbName("business_database");
//        datasourceInfoDTO.setSchema("business_database");
//        datasourceInfoDTO.setTableName("business_system");
//        datasourceInfoDTO.setDataTypeCode(DataSourceTypeEnum.MySQL.getVal());
//
//        String data = "{\"password\":\"Cdyanfa_123456\",\"dataTypeCode\":1,\"dataVersion\":\"5.x\",\"dataType\":\"MySQL\",\"driverClassName\":\"com.mysql.jdbc.Driver\",\"jdbcUrl\":\"**************************************************\",\"dataName\":\"sh_41\",\"businessUuid\":\"16793142841472\",\"username\":\"root\"}";
//        JSONObject jsonObject = JSONObject.parseObject(data);
//        datasourceInfoDTO.setDataJson(jsonObject.toJSONString());
//        datasourceInfoDTO.setDataJsonMap(jsonObject);
//        String ddl = DatasourceUtils.getDDL(datasourceInfoDTO);
//        Assert.assertTrue(StringUtils.isNotBlank(ddl));

    }

    /**
     * 获取正在使用的database
     */
    @Test
    public void getCurrentDatabase() {
        String currentDatabase = client.getCurrentDatabase(source);
        Assert.assertTrue(StringUtils.isNotBlank(currentDatabase));
    }

    /**
     * 根据 schema 获取表
     */
    @Test
    public void getTableBySchema() {
        List tableListBySchema = client.getTableListBySchema(source, SqlQueryDTO.builder().schema("dev").tableNamePattern("").limit(5).build());
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableListBySchema));
    }

    /**
     * 获取downloader
     */
    @Test
    public void getDownloader() throws Exception {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("LOADER_TEST").sql("select * from LOADER_TEST").build();
        IDownloader iDownloader = client.getDownloader(source, queryDTO);
        List<String> list = iDownloader.getMetaInfo();
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    }

    @Test
    public void isDatabaseExists() {
        assert client.isDatabaseExists(source, "dev");
    }

    @Test
    public void isTableExistsInDatabase() {
        assert client.isTableExistsInDatabase(source, null);
    }

    /**
     * 获取表 - 无视图
     */
    @Test
    public void tableListNotView() {
        List tableListBySchema = client.getTableListBySchema(source, SqlQueryDTO.builder().schema("dev").tableNamePattern("").limit(5).build());
        Assert.assertFalse(tableListBySchema.contains("LOADER_TEST_VIEW"));
    }

    /**
     * 获取表 - 有视图
     */
    @Test
    public void tableListContainView() {
        List tableListBySchema = client.getTableListBySchema(source, SqlQueryDTO.builder().schema("dev").tableNamePattern("").view(true).limit(5).build());
        Assert.assertTrue(tableListBySchema.contains("LOADER_TEST_VIEW"));
    }

    /**
     * 获取版本
     */
    @Test
    public void getVersion() {
        Assert.assertTrue(StringUtils.isNotBlank(client.getVersion(source)));
    }

    /**
     * 获取库的字符集
     */
    @Test
    public void getCharacterSetByDatabase() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        String characterSetByDatabase = client.getCharacterSetByDatabase(source, queryDTO);
        System.out.println(characterSetByDatabase);
    }

    /**
     * 获取库的时区
     */
    @Test
    public void getTimeZoneByDatabase() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        String characterSetByDatabase = client.getTimeZoneByDatabase(source, queryDTO);
        System.out.println(characterSetByDatabase);
    }

    @Test
    public void getDataSourceImport() {
        DatasourceInfoImportVO datasourceInfoImportVO = new DatasourceInfoImportVO();
        datasourceInfoImportVO.setDataType("MySQL");
        datasourceInfoImportVO.setIp("*************");
        datasourceInfoImportVO.setPort(3306L);
        datasourceInfoImportVO.setDbName("sh");
        datasourceInfoImportVO.setDataVersion("5.x");
        datasourceInfoImportVO.setDataName("sh-ces");
        datasourceInfoImportVO.setUserName("root");
        datasourceInfoImportVO.setPassWord("Cdyanfa_123456");
        datasourceInfoImportVO.setDataDesc("sssss");

        SqlQueryDTO queryDTO = SqlQueryDTO.builder().businessUuid("9fddaac3123b4c8b8f6aea86141bba78").datasourceInfoImportVO(datasourceInfoImportVO).build();
        DatasourceInfoDTO dataSourceImport = client.getDataSourceImport(source, queryDTO);
        System.out.println(dataSourceImport);
    }

    /**
     * 获取表或者视图--新
     */
    @Test
    public void getMedataDataTables() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().dbName("metadata").type("TABLE").tableNamePattern("10").build();
        List medataDataTables = client.getMedataDataTables(source, queryDTO);
        System.out.println(medataDataTables);

    }

    /**
     * 获取索引--新
     */
    @Test
    public void getIndexList() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().dbName("metadata").tableNamePattern("").build();
        List indexList = client.getIndexList(source, queryDTO);
        System.out.println(indexList);

    }

    /**
     * 获取索引字段--新
     */
    @Test
    public void getIndexColumn() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().dbName("metadata").tableName("table_1").indexName("table_1_idx_1").build();
        List indexColumn = client.getIndexColumn(source, queryDTO);
        System.out.println(indexColumn);

    }

    /**
     * 获取索引字段--新
     */
    @Test
    public void getFunctionList() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().dbName("metadata").type("FUNCTION").tableNamePattern("_1").build();
        List indexColumn = client.getFunctionList(source, queryDTO);
        System.out.println(indexColumn);

    }

    /**
     * 获取索引字段--新
     */
    @Test
    public void getFunctionArguments() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().dbName("metadata").objectName("function_1").build();
        List indexColumn = client.getFunctionArguments(source, queryDTO);
        System.out.println(indexColumn);

    }

    /**
     * 正则验证表名
     */
    @Test
    public void test() {
        String tableName = "test.user_aaa";
        String patternStr = "^test\\.aaa.*$";
        Pattern pattern = Pattern.compile(patternStr) ;
        Matcher matcher = pattern.matcher(tableName);
        if (matcher.matches()) {
            System.out.println("匹配");
        } else {
            System.out.println("不匹配");
        }
    }
}
