/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.mongo;

import com.dtstack.dtcenter.common.loader.common.exception.ErrorCode;
import com.dtstack.dtcenter.common.loader.common.nosql.AbsNoSqlClient;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.MongoSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.google.common.collect.Lists;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 15:24 2020/2/5
 * @Description：MongoDB 客户端
 */
@Slf4j
public class MongoDBClient<T> extends AbsNoSqlClient<T> {

    private static MongoExecutor mongoExecutor = MongoExecutor.getInstance();

    @Override
    public Boolean testCon(ISourceDTO iSource) {
        return MongoDBUtils.checkConnection(iSource);
    }

    @Override
    public List<String> getTableList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return MongoDBUtils.getTableList(sourceDTO, queryDTO);
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO source, SqlQueryDTO queryDTO) {
        return MongoDBUtils.getPreview(source, queryDTO);
    }

    @Override
    public int getPreviewRows(ISourceDTO source, SqlQueryDTO queryDTO) {
        List<List<Object>> preview = MongoDBUtils.getPreview(source, queryDTO);
        return preview.size();
    }

    @Override
    public List<String> getAllDatabases(ISourceDTO iSource, SqlQueryDTO queryDTO){
        return MongoDBUtils.getDatabaseList(iSource);
    }

    @Override
    public List<Map<String, Object>> executeQuery(ISourceDTO source, SqlQueryDTO queryDTO) {
        return mongoExecutor.execute(source, queryDTO);
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        return MongoDBUtils.getTableAndViewList(iSource,queryDTO);
    }
    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        List<ColumnMetaDTO> columnMetaData = MongoDBUtils.getColumnMetaData(iSource, queryDTO);
        List<ColumnMetaDTO> columnMetaDataNew=new ArrayList<>();
        for (ColumnMetaDTO columnMetaDatum : columnMetaData) {
            queryDTO.setColumnType(columnMetaDatum.getType());
            ColumnMetaDTO dataType = getDataType(iSource, queryDTO);
            if(Objects.nonNull(dataType)){
                columnMetaDatum.setDateType(dataType.getDateType());
                columnMetaDatum.setDataType(dataType.getDataType());
            }
            columnMetaDataNew.add(columnMetaDatum);

        }
        return columnMetaDataNew;
    }

    @Override
    public String getCurrentDatabase(ISourceDTO source) {
      return MongoDBUtils.getCurrentDatabase(source);
    }

    /**
     * 判断表是否存在
     */
    @Override
    public Boolean isTableExistsInDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        MongoSourceDTO mongoSourceDTO=(MongoSourceDTO) source;
        List<String> tableList = Lists.newArrayList();
        MongoClient mongoClient = null;
        try {
            String db = StringUtils.isNotEmpty(queryDTO.getDbName()) ? queryDTO.getDbName() : mongoSourceDTO.getSchema();
            mongoClient = MongoDBUtils.getClient(mongoSourceDTO);
            MongoDatabase mongoDatabase = mongoClient.getDatabase(db);
            MongoIterable<String> tableNames = mongoDatabase.listCollectionNames();
            for (String s : tableNames) {
                if(queryDTO.getTableName().equals(s)){
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("get tablelist exception  {}", mongoSourceDTO, e);
        } finally {
            if (!BooleanUtils.isTrue(MongoDBUtils.IS_OPEN_POOL.get()) && mongoClient != null) {
                mongoClient.close();
            }
        }
        return false;
    }
}