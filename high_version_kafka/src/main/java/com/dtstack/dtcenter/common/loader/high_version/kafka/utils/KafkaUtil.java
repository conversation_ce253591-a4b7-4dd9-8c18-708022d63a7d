package com.dtstack.dtcenter.common.loader.high_version.kafka.utils;

import com.alibaba.fastjson.JSON;
import com.dsg.database.datasource.dto.BrokersDTO;
import com.dsg.database.datasource.utils.NetUtils;
import com.dtstack.dtcenter.common.loader.common.utils.TelUtil;
import com.dtstack.dtcenter.common.loader.high_version.kafka.KafkaConsistent;
import com.dtstack.dtcenter.loader.dto.KafkaPartitionDTO;
import com.dtstack.dtcenter.loader.dto.source.KafkaSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import kafka.utils.ZkUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.NewPartitions;
import org.apache.kafka.common.Node;
import org.apache.kafka.common.security.JaasUtils;
import org.apache.zookeeper.data.Stat;
import scala.Option;
import scala.Tuple2;
import scala.collection.JavaConversions;
import scala.collection.Seq;
import sun.security.krb5.Config;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 22:46 2020/2/26
 * @Description：Kafka 工具类
 */
@Slf4j
public class KafkaUtil {

    public static final String EARLIEST = "earliest";
    private static final int MAX_POOL_RECORDS = 5;

    // 开启 kerberos 默认 sasl.kerberos.service.name
    private static final String DEFAULT_KERBEROS_NAME = "";

    private static final String BROKER_IDS_PATH = "/brokers/ids/";

    public static final String CONSUMER_OFFSET_TOPIC = "__consumer_offsets";
    private static final String BROKER_TOPICS_PATH = "/brokers/topics";
    private static void destroyProperty() {
        System.clearProperty("java.security.auth.login.config");
        System.clearProperty("javax.security.auth.useSubjectCredsOnly");
    }
    /**
     * 从 ZK 中获取所有的 Kafka broker 地址
     *
     * @param zkUrls zk 地址
     * @return kafka broker 地址
     */
    public static String getAllBrokersAddressFromZk(String zkUrls) {
        log.info("Obtain Kafka Broker address through ZK : {}", zkUrls);


        ZkUtils zkUtils = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            if (StringUtils.isBlank(zkUrls) || !TelUtil.checkTelnetAddr(zkUrls)) {
                throw new DtLoaderException("Please configure the correct zookeeper address");
            }
            zkUtils = ZkUtils.apply(zkUrls, KafkaConsistent.SESSION_TIME_OUT,
                    KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
            List<BrokersDTO> brokersByZk = getBrokersByZk(zkUtils);
            if (CollectionUtils.isNotEmpty(brokersByZk)) {
                for (BrokersDTO broker : brokersByZk) {
                    stringBuilder.append(",").append(broker.getHost()).append(":").append(broker.getPort());
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if (zkUtils != null) {
                zkUtils.close();
            }
        }
        return stringBuilder.toString();
    }
    /**
     * 获取 kafka broker 地址，如果 broker 填写为空则从 zookeeper 中获取
     *
     * @param sourceDTO kafka 数据源信息
     * @return kafka broker 地址
     */
    private static String getKafkaBroker(KafkaSourceDTO sourceDTO) {
        String brokerUrls = StringUtils.isEmpty(sourceDTO.getBrokerUrls()) ? getAllBrokersAddressFromZk(sourceDTO.getUrl()) : sourceDTO.getBrokerUrls();
        if (StringUtils.isBlank(brokerUrls)) {
            throw new DtLoaderException("failed to get broker from zookeeper.");
        }
        return brokerUrls;
    }

    /**
     * 初始化 Kafka 配置信息
     *
     * @param sourceDTO 数据源信息
     * @return kafka 配置
     */
    private synchronized static Properties initProperties(KafkaSourceDTO sourceDTO) {
        String brokerUrls = getKafkaBroker(sourceDTO);
        log.info("Initialize Kafka configuration information, brokerUrls : {}, kerberosConfig : {}", brokerUrls, sourceDTO.getKerberosConfig());
        Properties props = new Properties();
        if (StringUtils.isBlank(brokerUrls)) {
            throw new DtLoaderException("Kafka Broker address cannot be empty");
        }
        /* 定义kakfa 服务的地址，不需要将所有broker指定上 */
        props.put("bootstrap.servers", brokerUrls);
        /* 是否自动确认offset */
        props.put("enable.auto.commit", "true");
        /* 设置group id */
        props.put("group.id", KafkaConsistent.KAFKA_GROUP);
        /* 自动确认offset的时间间隔 */
        props.put("auto.commit.interval.ms", "1000");
        //heart beat 默认3s
        props.put("session.timeout.ms", "10000");
        //一次性的最大拉取条数
        props.put("max.poll.records", "5");
        props.put("auto.offset.reset", "earliest");
        /* key的序列化类 */
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        /* value的序列化类 */
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

        /*设置超时时间*/
        props.put("request.timeout.ms", "10500");

        // username 和 password 都为空的时候走 SASL/PLAIN 认证逻辑
        if (StringUtils.isNotBlank(sourceDTO.getUsername()) && StringUtils.isNotBlank(sourceDTO.getPassword())) {
            // SASL/PLAIN 相关设置
            props.put("security.protocol", "SASL_PLAINTEXT");
            props.put("sasl.mechanism", "PLAIN");
            props.put("sasl.jaas.config", String.format(KafkaConsistent.KAFKA_SASL_PLAIN_CONTENT, sourceDTO.getUsername(), sourceDTO.getPassword()));
            return props;
        }

        if (MapUtils.isEmpty(sourceDTO.getKerberosConfig())) {
            //不满足kerberos条件 直接返回
            return props;
        }
        // 只需要认证的用户名
        String kafkaKbrServiceName = MapUtils.getString(sourceDTO.getKerberosConfig(), HadoopConfTool.KAFKA_KERBEROS_SERVICE_NAME, DEFAULT_KERBEROS_NAME);
        if(StringUtils.isNotEmpty(kafkaKbrServiceName)){
            kafkaKbrServiceName = kafkaKbrServiceName.split("/")[0];
            // kerberos 相关设置
            props.put("security.protocol", "SASL_PLAINTEXT");
            // kafka broker的启动配置
            props.put("sasl.kerberos.service.name", kafkaKbrServiceName);
        }

        kafkaKbrServiceName = kafkaKbrServiceName.split("/")[0];
        String kafkaLoginConf = writeKafkaJaas(sourceDTO.getKerberosConfig());

        // 刷新kerberos认证信息，在设置完java.security.krb5.conf后进行，否则会使用上次的krb5文件进行 refresh 导致认证失败
        try {
            Config.refresh();
            javax.security.auth.login.Configuration.setConfiguration(null);
        } catch (Exception e) {
            log.error("Kafka kerberos authentication information refresh failed!");
        }

        props.put("sasl.mechanism", "GSSAPI");

        System.setProperty("java.security.auth.login.config", kafkaLoginConf);
        System.setProperty("javax.security.auth.useSubjectCredsOnly", "false");
        return props;

    }

    /**
     * 构建kafka node
     * @param node kafka副本信息
     * @return common-loader中定义的kafka副本信息
     */
    private static KafkaPartitionDTO.Node buildKafkaPartitionNode(Node node) {
        if (Objects.isNull(node)) {
            return KafkaPartitionDTO.Node.builder().build();
        }
        return KafkaPartitionDTO.Node.builder()
                .host(node.host())
                .id(node.id())
                .idString(node.idString())
                .port(node.port())
                .rack(node.rack())
                .build();
    }

    private static String writeKafkaJaas(Map<String, Object> kerberosConfig) {
        log.info("Initialize Kafka JAAS file, kerberosConfig : {}", kerberosConfig);
        if (MapUtils.isEmpty(kerberosConfig)) {
            return null;
        }

        // 处理 krb5.conf
        if (kerberosConfig.containsKey(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF)) {
            System.setProperty(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF, MapUtils.getString(kerberosConfig, HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF));
        }

        String keytabConf = MapUtils.getString(kerberosConfig, HadoopConfTool.PRINCIPAL_FILE);
        // 兼容历史数据
        keytabConf = StringUtils.isBlank(keytabConf) ? MapUtils.getString(kerberosConfig, HadoopConfTool.KAFKA_KERBEROS_KEYTAB) : keytabConf;
        try {
            File file = new File(keytabConf);
            File jaas = new File(file.getParent() + File.separator + "kafka_jaas.conf");
            if (jaas.exists()) {
                boolean checkDelete = jaas.delete();
                if (!checkDelete) {
                    log.error("delete file [{}] fail....", jaas.getAbsolutePath());
                }
            }

            String principal = MapUtils.getString(kerberosConfig, HadoopConfTool.PRINCIPAL);
            // 历史数据兼容
            principal = StringUtils.isBlank(principal) ? MapUtils.getString(kerberosConfig, "kafka.kerberos.principal") : principal;
            FileUtils.write(jaas, String.format(KafkaConsistent.KAFKA_JAAS_CONTENT, keytabConf, principal));
            String kafkaLoginConf = jaas.getAbsolutePath();
            log.info("Init Kafka Kerberos:login-conf:{}\n --sasl.kerberos.service.name:{}", keytabConf, principal);
            return kafkaLoginConf;
        } catch (IOException e) {
            throw new DtLoaderException(String.format("Writing to Kafka configuration file exception,%s", e.getMessage()), e);
        }
    }

    public static List<BrokersDTO> getBrokersByZk(ZkUtils zkUtils) {
        List<BrokersDTO> result = new ArrayList<>();
        List<Object> sortedBrokerList = JavaConversions.seqAsJavaList(zkUtils.getSortedBrokerList());
        for (Object o1 : sortedBrokerList) {
            System.out.println(o1);
            Tuple2<Option<String>, Stat> tuple = zkUtils.readDataMaybeNull(BROKER_IDS_PATH + o1);
            BrokersDTO broker = new BrokersDTO();
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            broker.setCreated(df.format(new Date(tuple._2.getCtime())));
            broker.setModify(df.format(new Date(tuple._2.getMtime())));
            String tupleString = new String(tuple._1.get());
            System.out.println(tupleString);
            String host = JSON.parseObject(tupleString).getString("host");
            int port = 0;
            if (StringUtils.isEmpty(host)) {
                String endpoints = JSON.parseObject(tupleString).getString("endpoints");
                List<String> endpointsList = JSON.parseArray(endpoints, String.class);

                if (endpointsList.size() >= 1) {
                    for (String endpointsStr : endpointsList) {
                        String tmp = endpointsStr.split("//")[1];
                        host = tmp.split(":")[0];
                        port = Integer.parseInt(tmp.split(":")[1]);
                        break;
                    }
                }
            } else {

                port = JSON.parseObject(tupleString).getInteger("port");
            }

            broker.setHost(host);
            broker.setPort(port);
            broker.setJmxPort(JSON.parseObject(tupleString).getInteger("jmx_port"));
            broker.setJmxPortStatus(NetUtils.telnet(broker.getHost(), broker.getJmxPort()));
            broker.setId((Integer) o1);
            result.add(broker);
        }
        return result;
    }

    public static Boolean createTopicPartitions(KafkaSourceDTO sourceDTO, String topicName,
                                                Integer partitions) {
        Properties defaultKafkaConfig = initProperties(sourceDTO);
        AdminClient client = AdminClient.create(defaultKafkaConfig);
        int existPartitions = (int) partitionNumbers(sourceDTO.getUrl(), topicName);
        // 获取kafka client
        try  {
            Map<String, NewPartitions> newPartitions = new HashMap<String, NewPartitions>();
            newPartitions.put(topicName, NewPartitions.increaseTo(existPartitions + partitions));
            client.createPartitions(newPartitions);
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }finally {
            client.close();
        }
        return true;
    }
    public static long partitionNumbers(String zkurls, String topic) {
        long count = 0L;
        if (CONSUMER_OFFSET_TOPIC.equals(topic)) {
            return count;
        }
        ZkUtils zkUtils = ZkUtils.apply(zkurls, KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        try {
            if (zkUtils.pathExists(BROKER_TOPICS_PATH + "/" + topic + "/partitions")) {
                Seq<String> subBrokerTopicsPaths = zkUtils.getChildren(BROKER_TOPICS_PATH + "/" + topic + "/partitions");
                count = JavaConversions.seqAsJavaList(subBrokerTopicsPaths).size();
            }
        } catch (Exception e) {
            log.error("Get topic partition numbers has error, msg is " + e.getCause().getMessage());
            e.printStackTrace();
        }
        if (zkUtils != null) {
            zkUtils.close();
        }
        return count;
    }


}
