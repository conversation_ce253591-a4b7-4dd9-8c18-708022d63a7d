<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>common-loader</artifactId>
        <groupId>com.dtstack.dtcenter</groupId>
        <version>2.0.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common.loader.huawei_hive3</artifactId>


    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <jar.package.name>huawei_hive3</jar.package.name>
        <jar.name>dtHuaweiHive3</jar.name>
        <hadoop.version>3.3.1-h0.cbu.mrs.330.r9</hadoop.version>
        <hive.version>3.1.0-h0.cbu.mrs.330.r9</hive.version>
        <log4j2.version>2.17.1</log4j2.version>
        <commons-lang3.version>3.9</commons-lang3.version>
        <commons-lang.version>2.6</commons-lang.version>
        <curator.version>5.1.0</curator.version>
        <woodstox-core.version>5.4.0</woodstox-core.version>
        <hadoop-shaded-guava.version>1.1.1</hadoop-shaded-guava.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <libthrift.version>0.14.1</libthrift.version>
        <commons-configuration2.version>2.1.1</commons-configuration2.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dtstack.dtcenter</groupId>
            <artifactId>common.loader.core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-annotations</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dtstack.dtcenter</groupId>
            <artifactId>common.loader.rdbms</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <artifactId>groovy-all</artifactId>
            <groupId>org.codehaus.groovy</groupId>
            <version>3.0.9</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <artifactId>log4j-core</artifactId>
            <groupId>org.apache.logging.log4j</groupId>
            <version>2.17.1</version>
        </dependency>

        <dependency>
            <artifactId>json-smart</artifactId>
            <groupId>net.minidev</groupId>
            <version>2.3.1</version>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.javacsv</groupId>
            <artifactId>javacsv</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.kerby</groupId>
            <artifactId>kerb-util</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <artifactId>snappy-java</artifactId>
            <groupId>org.xerial.snappy</groupId>
            <version>1.1.2.6</version>
        </dependency>

        <dependency>
            <artifactId>jackson-databind</artifactId>
            <groupId>com.fasterxml.jackson.core</groupId>
            <version>2.13.2.1</version>
        </dependency>


        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-jdbc</artifactId>
            <version>${hive.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-service-rpc</artifactId>
            <version>${hive.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-service</artifactId>
            <version>${hive.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-common</artifactId>
            <version>${hive.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-mapreduce-client-core</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hive.shims</groupId>
            <artifactId>hive-shims-common</artifactId>
            <version>${hive.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-standalone-metastore</artifactId>
            <version>${hive.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-serde</artifactId>
            <version>${hive.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-auth</artifactId>
            <version>${hadoop.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons-lang.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>${curator.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.woodstox</groupId>
            <artifactId>woodstox-core</artifactId>
            <version>${woodstox-core.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop.thirdparty</groupId>
            <artifactId>hadoop-shaded-guava</artifactId>
            <version>${hadoop-shaded-guava.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>${commons-collections.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
            <version>${libthrift.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-configuration2</artifactId>
            <version>${commons-configuration2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>

                            <shadedArtifactAttached>false</shadedArtifactAttached>
                            <createDependencyReducedPom>true</createDependencyReducedPom>
                            <promoteTransitiveDependencies>true</promoteTransitiveDependencies>

                            <transformers>

                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer"/>
                                <!-- The service transformer is needed to merge META-INF/services files -->
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ApacheNoticeResourceTransformer"/>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>reference.conf</resource>
                                </transformer>

                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>reference.conf</resource>
                                </transformer>

                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.XmlAppendingTransformer">
                                    <resource>core-default.xml</resource>
                                </transformer>

                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.XmlAppendingTransformer">
                                    <resource>core-site.xml</resource>
                                </transformer>

                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.XmlAppendingTransformer">
                                    <resource>yarn-default.xml</resource>
                                </transformer>

                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.XmlAppendingTransformer">
                                    <resource>mapred-default.xml</resource>
                                </transformer>

                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.XmlAppendingTransformer">
                                    <resource>mapred-site.xml</resource>
                                </transformer>
                            </transformers>

                            <artifactSet>
                                <excludes>
                                    <exclude>org.slf4j:slf4j-log4j12</exclude>
                                    <exclude>org.slf4j:slf4j-api</exclude>
                                    <exclude>netty-all:io.netty</exclude>
                                </excludes>
                            </artifactSet>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--拷贝第三方依赖文件到指定目录-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!--target/lib是依赖jar包的输出目录，根据自己喜好配置-->
                            <outputDirectory>target/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <!-- 确保解析到正确的版本 -->
                            <outputAbsoluteArtifactFilename>false</outputAbsoluteArtifactFilename>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                    <!-- 生成依赖清单 -->
                    <execution>
                        <id>list-dedp</id>
                        <phase>package</phase>
                        <goals>
                            <goal>list</goal>
                        </goals>
                        <configuration>
                            <!-- 设置输出文件 -->
                            <includeGroupIds>com.dtstack.dtcenter</includeGroupIds>
                            <outputFile>target/classes/dedplib-dependencies.txt</outputFile>
                            <!-- 确保解析到正确的版本 -->
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                    <execution>
                        <id>list-comm</id>
                        <phase>package</phase>
                        <goals>
                            <goal>list</goal>
                        </goals>
                        <configuration>
                            <!-- 设置输出文件 -->
                            <excludeGroupIds>com.dtstack.dtcenter</excludeGroupIds>
                            <outputFile>target/classes/lib-dependencies.txt</outputFile>
                            <!-- 确保解析到正确的版本 -->
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <!-- here the phase you need -->
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <tasks>
                                <copy file="${basedir}/target/${project.name}-${project.version}.jar"
                                      tofile="${basedir}/../build/pluginLibs/${jar.package.name}/${project.name}-${project.version}.jar" />
                                <!--移动lib下的依赖包到lib下-->
                                <copy todir="${basedir}/../build/pluginLibs/${jar.package.name}/">
                                    <fileset dir="${basedir}/target/lib">
                                        <include name="**/*.jar"/>
                                    </fileset>
                                </copy>
                                <!--移动class的txt到lib下-->
                                <copy todir="${basedir}/../build/pluginLibs/${jar.package.name}/">
                                    <fileset dir="${basedir}/target/classes">
                                        <include name="**/*dependencies.txt"/>
                                    </fileset>
                                </copy>
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>