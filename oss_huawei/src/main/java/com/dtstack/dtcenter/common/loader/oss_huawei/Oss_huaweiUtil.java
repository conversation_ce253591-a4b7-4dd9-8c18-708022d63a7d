/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.oss_huawei;


import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.OssHuaweiSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.obs.services.ObsClient;
import com.obs.services.ObsConfiguration;

import java.util.Objects;

/**
 * oss 工具类
 *
 * <AUTHOR>
 * date：Created in 上午10:07 2021/5/6
 * company: www.dtstack.com
 */
public class Oss_huaweiUtil {

    private static final Integer TIMEOUT = 60 * 1000;

    /**
     * 获取 aws s3 客户端
     *
     * @param sourceDTO 数据源信息
     * @return aws s3客户端
     */
    public static ObsClient getClient(OssHuaweiSourceDTO sourceDTO) {
        String endPoint = sourceDTO.getEndPoint();
        String accessKey = sourceDTO.getAccessKey();
        String secretKey = sourceDTO.getSecretKey();

        // 创建OSS客户端配置
        ObsConfiguration config = new ObsConfiguration();
        config.setSocketTimeout(TIMEOUT);
        config.setConnectionTimeout(TIMEOUT);
        config.setEndPoint(endPoint);
        // 创建OSSClient实例
        ObsClient obsClient = new ObsClient(accessKey, secretKey, config);
        return obsClient;
    }

    /**
     * 关闭 oss
     *
     * @param oss 客户端
     */
    public static void closeAmazonS3(ObsClient oss) {
        try{
            if (Objects.nonNull(oss)) {
                oss.close();
            }
        }catch (Exception e){

        }
    }

    /**
     * 强转 sourceDTO 为 AwsS3SourceDTO
     *
     * @param sourceDTO aws s3 sourceDTO
     * @return 转换后的 aws s3 sourceDTO
     */
    public static OssHuaweiSourceDTO convertSourceDTO(ISourceDTO sourceDTO) {
        if (!(sourceDTO instanceof OssHuaweiSourceDTO)) {
            throw new DtLoaderException("please pass in OssSourceDTO...");
        }
        return (OssHuaweiSourceDTO) sourceDTO;
    }
}
