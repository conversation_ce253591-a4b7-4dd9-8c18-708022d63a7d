/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.datahub;

import com.aliyun.datahub.client.DatahubClient;
import com.aliyun.datahub.client.model.*;
import com.dtstack.dtcenter.common.loader.common.exception.ErrorCode;
import com.dtstack.dtcenter.common.loader.common.nosql.AbsNoSqlClient;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.DatahubSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * aws s3 Client
 *
 * <AUTHOR>
 * date：Created in 上午9:46 2021/5/6
 * company: www.dtstack.com
 */
public class Datahub_Client<T> extends AbsNoSqlClient<T> {

    /**
     * s3 object 前置查询需要以 .* 结尾
     */
    private static final String SEARCH_PREFIX_SING = ".*";

    @Override
    public Boolean testCon(ISourceDTO source) {
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        DatahubClient datahubClient = null;
        try {
            datahubClient = DatahubUtil.getClient(sourceDTO);
            datahubClient.listProject();
        } catch (Exception e) {
            throw new DtLoaderException(String.format("datahub connection failed : %s", e.getMessage()), e);
        } finally {
            DatahubUtil.closeAmazonS3(datahubClient);
        }
        return true;
    }

    @Override
    public List<String> getTableList(ISourceDTO source, SqlQueryDTO queryDTO) {
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        String bucket = queryDTO.getSchema();
        if (StringUtils.isBlank(bucket)) {
            throw new DtLoaderException("bucket cannot be blank....");
        }
        String tableNamePattern = queryDTO.getTableNamePattern();
        // 是否匹配查询
        boolean isPattern = StringUtils.isNotBlank(tableNamePattern);
        // 仅支持前置匹配
        boolean isPrefix = isPattern && tableNamePattern.endsWith(SEARCH_PREFIX_SING);
        DatahubClient oss = null;
        List<String> topicNames=new ArrayList<>();
        try {
            oss = DatahubUtil.getClient(sourceDTO);

            ListTopicResult listTopicResult = oss.listTopic(bucket);
            topicNames = listTopicResult.getTopicNames();
            if (Objects.isNull(topicNames)) {
                return Lists.newArrayList();
            }
       } catch (Exception e) {
            throw new DtLoaderException(String.format("datahub get topics failed : %s", e.getMessage()), e);
        } finally {
            DatahubUtil.closeAmazonS3(oss);
        }
        if (isPattern && !isPrefix) {
            topicNames = topicNames.stream().filter(table -> StringUtils.equalsIgnoreCase(table, tableNamePattern)).collect(Collectors.toList());
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            topicNames = topicNames.stream().limit(queryDTO.getLimit()).collect(Collectors.toList());
        }
        return topicNames;
    }

    @Override
    public List<String> getTableListBySchema(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getTableList(source, queryDTO);
    }

    @Override
    public List<String> getAllDatabases(ISourceDTO source, SqlQueryDTO queryDTO) {
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        DatahubClient datahubClient = null;
        List<String> result=new ArrayList<>();
        try {
            datahubClient = DatahubUtil.getClient(sourceDTO);
            ListProjectResult listProjectResult = datahubClient.listProject();
            result=listProjectResult.getProjectNames();
        } catch (Exception e) {
            throw new DtLoaderException(String.format("datahub get projects failed : %s", e.getMessage()), e);
        } finally {
            DatahubUtil.closeAmazonS3(datahubClient);
        }
        if (StringUtils.isNotBlank(queryDTO.getSchema())) {
            result = result.stream().filter(bucket -> StringUtils.containsIgnoreCase(bucket, queryDTO.getSchema().trim())).collect(Collectors.toList());
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            result = result.stream().limit(queryDTO.getLimit()).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO source, SqlQueryDTO queryDTO) {
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        DatahubClient datahubClient = null;
        List<TableViewDTO> result=new ArrayList<>();
        try {
            datahubClient = DatahubUtil.getClient(sourceDTO);
            ListTopicResult listTopicResult = datahubClient.listTopic(queryDTO.getSchema());
            for (String topicName : listTopicResult.getTopicNames()) {
                TableViewDTO tableViewDTO=new TableViewDTO(topicName,"TABLE");
                result.add(tableViewDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("datahub get topics failed : %s", e.getMessage()), e);
        } finally {
            DatahubUtil.closeAmazonS3(datahubClient);
        }
        return result;
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO source, SqlQueryDTO queryDTO) {
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        DatahubClient datahubClient = null;
        List<ColumnMetaDTO> columnMetaDTOS=new ArrayList<>();
        try {
            datahubClient = DatahubUtil.getClient(sourceDTO);
            RecordSchema schema = datahubClient.getTopic(queryDTO.getSchema(), queryDTO.getTableName()).getRecordSchema();
            if(Objects.isNull(schema)){
                return columnMetaDTOS;
            }
            List<Field> fields = schema.getFields();
            for (Field field : fields) {
                ColumnMetaDTO columnMetaDTO=new ColumnMetaDTO();
                columnMetaDTO.setKey(field.getName());
                columnMetaDTO.setType(field.getType().name());
                columnMetaDTO.setComment(field.getComment());
                //因集成使用，给默认值
                columnMetaDTO.setPrecision(Integer.MAX_VALUE);
                columnMetaDTO.setScale(0);
                columnMetaDTOS.add(columnMetaDTO);
            }
            List<ColumnMetaDTO> columnMetaDataNew=new ArrayList<>();
            for (ColumnMetaDTO columnMetaDatum : columnMetaDTOS) {
                String type = columnMetaDatum.getType();
                queryDTO.setColumnType(type);
                ColumnMetaDTO dataType = getDataType(source, queryDTO);
                if(Objects.nonNull(dataType)){
                    columnMetaDatum.setDataType(dataType.getDataType());
                }
                columnMetaDataNew.add(columnMetaDatum);
            }
            return columnMetaDataNew;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("datahub get topicsSchema failed : %s", e.getMessage()), e);
        } finally {
            DatahubUtil.closeAmazonS3(datahubClient);
        }

    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO source, SqlQueryDTO queryDTO) {
        List<List<Object>> list=new ArrayList<>();
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        DatahubClient datahubClient = null;
        try {
            datahubClient = DatahubUtil.getClient(sourceDTO);
            //获取shard
            ListShardResult listShardResult = datahubClient.listShard(queryDTO.getSchema(), queryDTO.getTableName());
            List<ShardEntry> shards = listShardResult.getShards();
            String shardId="";
            if(shards.isEmpty()){
                shardId="0";
            }else{
                ShardEntry shardEntry = shards.get(0);
                shardId = shardEntry.getShardId();
            }
            // 获取 shard 的 cursor
            GetCursorResult cursorResult = datahubClient.getCursor(queryDTO.getSchema(), queryDTO.getTableName(), shardId, CursorType.OLDEST);
            // 获取数据记录
            GetRecordsResult recordsResult = datahubClient.getRecords(queryDTO.getSchema(), queryDTO.getTableName(), shardId, cursorResult.getCursor(), queryDTO.getPreviewNum());
            // 打印数据记录
            List<RecordEntry> records = recordsResult.getRecords();
            for (RecordEntry record : records) {
                RecordData recordData = record.getRecordData();
                if(recordData instanceof  TupleRecordData ){
                    TupleRecordData data = (TupleRecordData) recordData;
                    RecordSchema recordSchema = data.getRecordSchema();
                    List<Object> dataList=new ArrayList<>();
                    for (Field field : recordSchema.getFields()) {
                        Object field1 = data.getField(field.getName());
                        dataList.add(field1);
                    }
                    list.add(dataList);
                    continue;
                }

                if(recordData instanceof  BlobRecordData ){
                    BlobRecordData data = (BlobRecordData) recordData;
                    byte[] data1 = data.getData();
                    List<Object> dataList=new ArrayList<>();
                    dataList.add(new String(data1));
                    list.add(dataList);
                }
            }
            return list;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("datahub get data failed : %s", e.getMessage()), e);
        } finally {
            DatahubUtil.closeAmazonS3(datahubClient);
        }
    }

    @Override
    public int getPreviewRows(ISourceDTO source, SqlQueryDTO queryDTO) {
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        DatahubClient client = null;
        int  num=0;
        try {
            client = DatahubUtil.getClient(sourceDTO);
            //获取shard
            ListShardResult listShardResult = client.listShard(queryDTO.getSchema(), queryDTO.getTableName());
            List<ShardEntry> shards = listShardResult.getShards();
            String shardId="";
            if(shards.isEmpty()){
                shardId="0";
            }else{
                ShardEntry shardEntry = shards.get(0);
                shardId = shardEntry.getShardId();
            }
            // 获取 Shard 的最新游标
            GetCursorResult latestCursor = client.getCursor(queryDTO.getSchema(), queryDTO.getTableName(), shardId, CursorType.OLDEST);
            // 获取数据记录
            GetRecordsResult recordsResult = client.getRecords(queryDTO.getSchema(), queryDTO.getTableName(), shardId, latestCursor.getCursor(),10000);
            // 打印数据记录
            List<RecordEntry> records = recordsResult.getRecords();
            num=records.size();
            return num ;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("datahub get dataNum failed : %s", e.getMessage()), e);
        } finally {
            DatahubUtil.closeAmazonS3(client);
        }
    }

    /**
     *  获取备注
     * @param source
     * @param queryDTO
     * @return
     */
    @Override
    public String getTableMetaComment(ISourceDTO source, SqlQueryDTO queryDTO) {
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        DatahubClient datahubClient = null;
        String comment="";
        try {
            datahubClient = DatahubUtil.getClient(sourceDTO);
            // 获取 topic 详细信息
            GetTopicResult topic = datahubClient.getTopic(queryDTO.getSchema(), queryDTO.getTableName());
            comment = topic.getComment();
            return  comment;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("datahub get comment failed : %s", e.getMessage()), e);
        } finally {
            DatahubUtil.closeAmazonS3(datahubClient);
        }
    }

    /**
     *  判断topic  表类型tuple，blob
     * @param source
     * @param queryDTO
     * @return
     */
    @Override
    public String getTableLabel(ISourceDTO source, SqlQueryDTO queryDTO) {
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        DatahubClient datahubClient = null;
        String type="";
        try {
            datahubClient = DatahubUtil.getClient(sourceDTO);
            // 获取 topic 详细信息
            GetTopicResult topic = datahubClient.getTopic(queryDTO.getSchema(), queryDTO.getTableName());
            // 获取 topic 类型
            RecordType topicType = topic.getRecordType();
            // 判断 topic 类型
            if (topicType == RecordType.TUPLE) {
                type=RecordType.TUPLE.name();
            } else if (topicType == RecordType.BLOB) {
                type=RecordType.BLOB.name();
            }
            return  type;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("datahub get topicType failed : %s", e.getMessage()), e);
        } finally {
            DatahubUtil.closeAmazonS3(datahubClient);
        }
    }
    /**
     * 表是否存在
     */
    @Override
    public Boolean isTableExistsInDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        DatahubSourceDTO sourceDTO = DatahubUtil.convertSourceDTO(source);
        DatahubClient datahubClient = null;
        try {
            datahubClient = DatahubUtil.getClient(sourceDTO);
            // 获取 topic 详细信息
            GetTopicResult topic = datahubClient.getTopic(queryDTO.getSchema(), queryDTO.getTableName());
            if (topic == null) {
                return false;
            }
            return  true;
        }catch (Exception e){
            return false;
        }
    }


}
