package com.dtstack.dtcenter.common.loader.db2;

public class SqlConstants {

    //函数常量
    public static final String FUNCTION_TYPE = "FUNCTION";
    public static final String FUNCTION = "F";
    //存储过程
    public static final String  PROCEDURE_TYPE = "PROCEDURE";
    public static final String  PROCEDURE = "P";
    //表
    public static final String TABLE_TYPE = "TABLE";
    //视图
    public static final String VIEW_TYPE = "VIEW";
    // 视图
    public static final String VIEW = "'VIEW'";

    // 普通表
    public static final String BASE_TABLE = "'BASE TABLE'";

    // 获取指定数据库下的表

    public static final String GET_MAX_CONNECTIONS =
            "SELECT  value as max_connections FROM sysibmadm.dbcfg WHERE name = 'maxappls'";


    public static final String GET_METADATA_PRIVILEGES =
            "SELECT CURRENT_USER AS user FROM sysibm.sysdummy1";


    public static final String SHOW_GRANTS =
            "SELECT * FROM SYSCAT.DBAUTH WHERE GRANTEE = CURRENT_USER";

    public static final String GET_TABLE_SCHEMA_SQL =
            "SELECT TABNAME AS table_name, TYPE AS table_type " +
                    "FROM SYSCAT.TABLES " +
                    "WHERE TABSCHEMA = '%s' " +
                    "AND TYPE ='T' ";

    // 获取指定数据库下的视图
    public static final String GET_TABLE_VIEW_SQL =
            "SELECT TABNAME AS table_name, TYPE AS table_type " +
                    "FROM SYSCAT.TABLES " +
                    "WHERE TABSCHEMA = '%s' " +
                    "AND TYPE ='V' ";

    //表名模糊搜索sql
    // 表名正则匹配模糊查询
    public static final String SEARCH_SQL = " AND TABNAME LIKE  '%%%s%%'  ";


    //获取指定库下或者指定表下的索引
    public static final String GET_INDEX_SQL =
            "SELECT INDNAME AS index_name, " +
                    "TABNAME AS table_name, " +
                    "COLNAMES AS index_definition, " +
                    "UNIQUERULE AS index_type " +
                    "FROM SYSCAT.INDEXES " +
                    "WHERE TABSCHEMA = '%s'";


    /**
     * 索引搜索sql
     */
    public static final String SEARCH_INDEX_SQL = " AND INDNAME LIKE  '%%%s%%'  ";


    public static final String GET_INDEX_COLUMN_SQL =
            "SELECT i.TABNAME AS table_name, " +
                    "i.INDNAME AS index_name, " +
                    "ic.COLNAME AS column_name, " +
                    "i.INDEXTYPE AS index_type " +
                    "FROM SYSCAT.INDEXES i " +
                    "JOIN SYSCAT.INDEXCOLUSE ic " +
                    "ON i.INDSCHEMA = ic.INDSCHEMA AND i.INDNAME = ic.INDNAME " +
                    "WHERE i.TABSCHEMA = '%s' " +
                    "AND i.TABNAME = '%s' " +
                    "AND i.INDNAME = '%s'";



    //获取指定库得函数或者存储过程
    public static final String GET_PRODUCE_SQL =
            "SELECT ROUTINESCHEMA AS ROUTINE_SCHEMA, " +
                    "ROUTINENAME AS ROUTINE_NAME, " +
                    "ROUTINETYPE AS ROUTINE_TYPE " +
                    "FROM SYSCAT.ROUTINES " +
                    "WHERE ROUTINESCHEMA = '%s' " +
                    "AND ROUTINETYPE = '%s'";



    public static final String SEARCH_PRODUCE_SQL = " AND ROUTINENAME LIKE  '%%%s%%'  ";

    //获取指定库得函数或者存储过程参数
    public static final String GET_PRODUCE_ARGUMENTS_SQL =
            "SELECT " +
                    "    ROUTINESCHEMA AS PACKAGE_NAME, " +
                    "    ROUTINENAME AS SPECIFIC_NAME, " +
                    "    PARMNAME AS PARAMETER_NAME, " +
                    "    TYPENAME AS DATA_TYPE, " +
                    "    CASE PARM_MODE " +
                    "        WHEN 'I' THEN 'IN' " +
                    "        WHEN 'O' THEN 'OUT' " +
                    "        WHEN 'B' THEN 'INOUT' " +
                    "        ELSE PARM_MODE " +
                    "    END AS PARAMETER_MODE, " +
                    "    ROW_NUMBER() OVER (ORDER BY ORDINAL) AS ORDINAL_POSITION, " +
                    "    LENGTH AS CHARACTER_OCTET_LENGTH, " +
                    "    SCALE AS NUMERIC_SCALE, " +
                    "    LENGTH AS NUMERIC_PRECISION " +
                    "FROM SYSCAT.PROCPARMS " +
                    "WHERE ROUTINESCHEMA = '%s' " +
                    "AND ROUTINENAME = '%s' " +
                    "ORDER BY ORDINAL";


}
