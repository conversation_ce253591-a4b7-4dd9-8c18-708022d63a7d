package com.dtstack.dtcenter.common.loader.oracle;

public class SqlConstants {

    //函数常量
    public static final String FUNCTION_TYPE = "FUNCTION";
    //存储过程
    public static final String PROCEDURE_TYPE = "PROCEDURE";
    //表
    public static final String TABLE_TYPE = "TABLE";
    //视图
    public static final String VIEW_TYPE = "VIEW";


    //关联
    public static final String UNION_ALL = " %s UNION ALL %s";

    // oracle获取指定schema下的表
    public static final String SHOW_TABLE_BY_SCHEMA_SQL_NEW = " SELECT\n" +
            "\tt.table_name AS NAME,\n" +
            "\t'TABLE' AS TYPE\n" +
            "FROM\n" +
            "\tall_tables t\n" +
            "WHERE\n" +
            "\tt.owner = '%s' %s ";
    // oracle获取指定schema下的视图
    public static final String SHOW_VIEW_BY_SCHEMA_SQL_NEW = "SELECT\n" +
            "    v.view_name AS NAME,\n" +
            "    'VIEW' AS TYPE\n" +
            "FROM\n" +
            "    all_views v\n" +
            "WHERE\n" +
            "    v.owner = '%s' %s ";

    // oracle获取指定schema下的索引:oracle 索引无备注
    public static final String SHOW_INDEX_BY_SCHEMA_SQL_NEW = "SELECT\n" +
            "\tINDEX_NAME,\n" +
            "\tTABLE_NAME,\n" +
            "\tTABLE_OWNER,\n" +
            "\tUNIQUENESS,\n" +
            "\tINDEX_TYPE \n" +
            "FROM\n" +
            "\tALL_INDEXES \n" +
            "WHERE\n" +
            "TABLE_OWNER = '%s'";


    public static final String SHOW_INDEX_COLUMN_BY_SCHEMA_SQL_NEW = "SELECT\n" +
            "\tINDEX_NAME,\n" +
            "\tTABLE_NAME,\n" +
            "\tCOLUMN_NAME,\n" +
            "\tCOLUMN_POSITION,\n" +
            "\tDESCEND \n" +
            "FROM\n" +
            "\tALL_IND_COLUMNS\n" +
            "WHERE\n" +
            "TABLE_OWNER = '%s' AND TABLE_NAME = '%s' AND INDEX_NAME='%s'" ;

    //oracle获取指定schema下的函数或者存储过程
    public static final String SHOW_FUNCTION_BY_SCHEMA_SQL_NEW = "SELECT\n" +
            "\tOBJECT_NAME,\n" +
            "\tOBJECT_TYPE,\n" +
            "\tOWNER,\n" +
            "\tSTATUS,\n" +
            "\tDBMS_METADATA.GET_DDL ( OBJECT_TYPE, OBJECT_NAME, OWNER ) AS DDL_SCRIPT \n" +
            "FROM\n" +
            "\tALL_OBJECTS \n" +
            "WHERE\n" +
            "\tOWNER = '%s' \n" +
            "\tAND OBJECT_TYPE = '%s'" ;

    //oracle获取指定schema下的函数或者存储过程字段
    public static final String SHOW_FUNCTION_ARGUMENTS_BY_SCHEMA_SQL_NEW = "SELECT \n" +
            "\tOBJECT_NAME,\n" +
            "  ARGUMENT_NAME,\n" +
            "\tPOSITION,\n" +
            "\tSEQUENCE,\n" +
            "\tDATA_TYPE,\n" +
            "\tIN_OUT,\n" +
            "\tDEFAULTED,\n" +
            "\tDEFAULT_VALUE,\n" +
            "\tDATA_LENGTH,\n" +
            "\tDATA_PRECISION,\n" +
            "\tDATA_SCALE,\n" +
            "\tCHARACTER_SET_NAME \n" +
            "FROM ALL_ARGUMENTS\n" +
            "WHERE OWNER = '%s'\n" +
            "AND OBJECT_NAME = '%s'";

    //表名模糊搜索sql
    // 表名正则匹配模糊查询
    public static final String SEARCH_SQL = " AND name LIKE '%%%s%%' ";
    /**
     * 索引名模糊搜索sql
     */
    public static final String SEARCH_INDEX_SQL = " AND INDEX_NAME LIKE  '%%%s%%'  ";

    /**
     * 函数名模糊搜索sql
     */
    public static final String SEARCH_FUNCTION_SQL = " AND OBJECT_NAME LIKE  '%%%s%%'  ";

}

