/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.huaweiHdfs;

import com.dtstack.dtcenter.loader.dto.source.HuaweiHdfsSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import org.apache.commons.lang3.StringUtils;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 16:53 2020/2/27
 * @Description：HDFS 连接工厂
 */
public class HdfsConnFactory {

    public Boolean testConn(ISourceDTO iSource) {
        HuaweiHdfsSourceDTO HuaweiHdfsSourceDTO = (HuaweiHdfsSourceDTO) iSource;
        if (StringUtils.isBlank(HuaweiHdfsSourceDTO.getDefaultFS())) {
            throw new DtLoaderException("defaultFS incorrect format");
        }

        return HdfsOperator.checkConnection(HuaweiHdfsSourceDTO);
    }
}
