/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.goldenDb;

import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.utils.CollectionUtil;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.common.utils.ReflectUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.DsIndexDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.GoldenDbSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Mysql5SourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.*;
import java.util.*;
import java.util.regex.Pattern;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 17:18 2020/1/3
 * @Description：Mysql 客户端
 */
@Slf4j
public class GoldenDbClient extends AbsRdbmsClient {
    private static final String TABLE_NAME = "table_name";
    private static final String TABLE_TYPE = "table_type";

    // 获取正在使用数据库
    private static final String CURRENT_DB = "select database()";

    // 模糊查询数据库
    private static final String SHOW_DB_LIKE = "show databases like '%s'";

    private static final String DONT_EXIST = "doesn't exist";

    // 获取指定数据库下的表
    private static final String SHOW_TABLE_BY_SCHEMA_SQL = "select table_name from information_schema.tables where table_schema='%s' and table_type in (%s) %s";

    // 获取指定数据库下的表（结果包含字段类型）
    private static final String SHOW_TABLE_TYPE_BY_SCHEMA_SQL = "select table_name,table_type from information_schema.tables where table_schema='%s' and table_type in (%s) %s";

    // 视图
    private static final String VIEW = "'VIEW'";

    /**
     * table count sql
     */
    private static final String MYSQL_TABLE_ROW = "SELECT TABLE_ROWS FROM information_schema.`TABLES` where TABLE_SCHEMA = '%s' and TABLE_NAME = '%s'";

    // 普通表
    private static final String BASE_TABLE = "'BASE TABLE'";

    // 表名正则匹配模糊查询
    private static final String SEARCH_SQL = " AND table_name LIKE '%s' ";

    // 限制条数语句
    private static final String LIMIT_SQL = " limit %s ";

    // 创建数据库
    private static final String CREATE_SCHEMA_SQL_TMPL = "create schema %s ";

    // 判断table是否在schema中
    private static final String TABLE_IS_IN_SCHEMA = "select table_name from information_schema.tables where table_schema='%s' and table_name = '%s'";

    // 获取当前版本号
    private static final String SHOW_VERSION = "select version()";

    private static final Map<Short, String> indexTypeMap = new HashMap<Short, String>() {
        {
            put((short) 0, "tableIndexStatistic");
            put((short) 1, "tableIndexClustered");
            put((short) 2, "tableIndexHashed");
            put((short) 3, "tableIndexOther");
        }
    };


    @Override
    protected ConnFactory getConnFactory() {
        return new GoldenDbConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.GoldenDB;
    }

    @Override
    protected String transferTableName(String tableName) {
        return tableName.contains("`") ? tableName : String.format("`%s`", tableName);
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        return getTableListBySchema(iSource, queryDTO);
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        String schema = queryDTO.getSchema();
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            log.info("schema is empty，get current used schema!");
            // 获取当前数据库
            try {
                schema = getCurrentDatabase(iSource);
            } catch (Exception e) {
                throw new DtLoaderException(String.format("get current used database error！,%s", e.getMessage()), e);
            }
        }
        log.info("current used schema：{}", schema);
        StringBuilder constr = new StringBuilder();
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            constr.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        List<String> tableType = Lists.newArrayList(BASE_TABLE);
        // 获取视图
        if (BooleanUtils.isTrue(queryDTO.getView())) {
            tableType.add(VIEW);
        }
        List<TableViewDTO> tableViewList = new ArrayList<>();
        String sql = String.format(SHOW_TABLE_TYPE_BY_SCHEMA_SQL, schema, String.join(",", tableType), constr.toString());
        Integer fetchSize = ReflectUtil.fieldExists(SqlQueryDTO.class, "fetchSize") ? queryDTO.getFetchSize() : null;
        List<Map<String, Object>> tableList = queryCustomColumn(iSource, fetchSize, sql, "get table exception according to schema...");
        for (Map<String, Object> table : tableList) {
            if (MapUtils.isNotEmpty(table)) {
                TableViewDTO tableViewDTO = new TableViewDTO();
                for (Map.Entry<String, Object> entry : table.entrySet()) {
                    String key = entry.getKey();
                    String value = String.valueOf(entry.getValue());
                    if (TABLE_TYPE.equalsIgnoreCase(key)) {
                        tableViewDTO.setType("BASE TABLE".equalsIgnoreCase(value) ? "TABLE" : value.toUpperCase());
                    }
                    if (TABLE_NAME.equalsIgnoreCase(key)) {
                        tableViewDTO.setName(value);
                    }
                }
                tableViewList.add(tableViewDTO);
            }
        }
        return tableViewList;
    }

    @Override
    public String getCharacterSet(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        String character = getCharacterCollation(iSource, queryDTO);
        if (character != null && character.indexOf("_") > 0) {
            String[] s = character.split("_");
            return s[0];
        } else {
            return "";
        }
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(goldenDbSourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        long tableRow = 0L;
        try {
            statement = goldenDbSourceDTO.getConnection().createStatement();
            String currentDatabase = getCurrentDatabase(iSource);
            resultSet = statement.executeQuery(String.format(MYSQL_TABLE_ROW, currentDatabase, queryDTO.getTableName()));
            while (resultSet.next()) {
                tableRow = resultSet.getInt(1);
                return tableRow;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(goldenDbSourceDTO, clearStatus));
        }
        return tableRow;
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) source;
        Integer clearStatus = beforeColumnQuery(goldenDbSourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        String currentDatabase =queryDTO.getDbName();
        try {
            statement = goldenDbSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format("SHOW TABLE STATUS FROM %s WHERE NAME = '%s'", currentDatabase, queryDTO.getTableName()));
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);

                if (dbTableName.equalsIgnoreCase(queryDTO.getTableName())) {
                    String character = resultSet.getString(DtClassConsistent.PublicConsistent.collation);
                    return character;
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(goldenDbSourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    protected String doDealType(ResultSetMetaData rsMetaData, Integer los) throws SQLException {
        int columnType = rsMetaData.getColumnType(los + 1);
        // text,mediumtext,longtext的jdbc类型名都是varchar，需要区分。不同的编码下，最大存储长度也不同。考虑1，2，3，4字节的编码

        if (columnType != Types.LONGVARCHAR) {
            return super.doDealType(rsMetaData, los);
        }

        int precision = rsMetaData.getPrecision(los + 1);
        if (precision >= 16383 && precision <= 65535) {
            return "TEXT";
        }

        if (precision >= 4194303 && precision <= 16777215) {
            return "MEDIUMTEXT";
        }

        if (precision >= 536870911 && precision <= 2147483647) {
            return "LONGTEXT";
        }

        return super.doDealType(rsMetaData, los);
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(goldenDbSourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            statement = goldenDbSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery("show table status");
            while (resultSet.next()) {
                String dbTableName = resultSet.getString(1);

                if (dbTableName.equalsIgnoreCase(queryDTO.getTableName())) {
                    return resultSet.getString(DtClassConsistent.PublicConsistent.COMMENT);
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(goldenDbSourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) source;
        GoldenDbDownloader dbDownloader= new GoldenDbDownloader(getCon(source), queryDTO.getSql(), goldenDbSourceDTO.getSchema());
        dbDownloader.configure();
        return dbDownloader;
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }


    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        Set<ColumnMetaDTO> columns = new HashSet<>();
        List<ColumnMetaDTO> newColumns = new ArrayList<>();
        Statement statement = null;
        ResultSet rs = null;
        //修改 dgr 20220722
        ResultSet rsColumn = null;
        ResultSet pkRs = null;
        ResultSet fkRs = null;
        ResultSet uniqueRs = null;
        ResultSet allIndexRs = null;

        try {
            log.info("------------------开始getColumnMetaData------------------");
            DatabaseMetaData metaData = rdbmsSourceDTO.getConnection().getMetaData();

            log.info("------------------start------------------");
            String catalog = rdbmsSourceDTO.getConnection().getCatalog();
            if(StringUtils.isNotEmpty(queryDTO.getSchema())){
                catalog=queryDTO.getSchema();
            }
            pkRs = metaData.getPrimaryKeys(catalog, rdbmsSourceDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> pkList = new ArrayList<>();
            while (pkRs.next()) {
                pkList.add(pkRs.getString("COLUMN_NAME"));
            }

            log.info("------------------执行pkRs结束------------------");

            fkRs = metaData.getExportedKeys(catalog, rdbmsSourceDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> fkList = new ArrayList<>();
            while (fkRs.next()) {
                fkList.add(fkRs.getString("PKCOLUMN_NAME"));
            }

            log.info("------------------执行fkRs结束------------------");
            ArrayList<String> uniqueList = new ArrayList<>();
            ArrayList<DsIndexDTO> allIndexList = new ArrayList<>();

            rsColumn = metaData.getColumns(catalog, rdbmsSourceDTO.getSchema(), queryDTO.getTableName(), null);
            while (rsColumn.next()) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setPart(false);
                columnMetaDTO.setKey(rsColumn.getString("COLUMN_NAME"));
                columnMetaDTO.setType(rsColumn.getString("TYPE_NAME"));
                columnMetaDTO.setComment(rsColumn.getString("REMARKS"));
                columnMetaDTO.setScale(rsColumn.getInt("DECIMAL_DIGITS"));
                columnMetaDTO.setLength(rsColumn.getInt("COLUMN_SIZE"));
                columnMetaDTO.setDataType(rsColumn.getInt("DATA_TYPE"));
                columnMetaDTO.setDefaultValue(rsColumn.getString("COLUMN_DEF"));
                columnMetaDTO.setNotNullFlag("no".equals(rsColumn.getString("IS_NULLABLE").toLowerCase()));
                if (pkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setPkflag(true);
                } else {
                    columnMetaDTO.setPkflag(false);
                }
                if (fkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setFkflag(true);
                } else {
                    columnMetaDTO.setFkflag(false);
                }
                if (uniqueList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setUniqueFlag(true);
                } else {
                    columnMetaDTO.setUniqueFlag(false);
                }

                for (DsIndexDTO dsIndexDTO : allIndexList
                ) {
                    if (rsColumn.getString("COLUMN_NAME").equals(dsIndexDTO.getColumnName())) {
                        columnMetaDTO.setIndexType(indexTypeMap.get(dsIndexDTO.getType()));
                    }
                }

                columns.add(columnMetaDTO);
            }

            log.info("------------------执行getColumns结束------------------");

            statement = rdbmsSourceDTO.getConnection().createStatement();
            statement.setMaxRows(1);
            String queryColumnSql =
                    "select " + CollectionUtil.listToStr(queryDTO.getColumns()) + " from " + transferSchemaAndTableName(rdbmsSourceDTO, queryDTO) + " where 1=2";

            rs = statement.executeQuery(queryColumnSql);

            log.info("------------------执行select结束------------------");

            ResultSetMetaData rsMetaData = rs.getMetaData();
            int columnCount = rsMetaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMetaData.getColumnName(i);
                for (ColumnMetaDTO columnMetaDTO : columns) {
                    if (columnMetaDTO.getKey().equalsIgnoreCase(columnName)) {
                        columnMetaDTO.setPrecision(rsMetaData.getPrecision(i));
                        columnMetaDTO.setDateType(rsMetaData.getColumnClassName(i));
                        newColumns.add(columnMetaDTO);
                    }
                }
            }

        } catch (SQLException e) {
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the meta information of the fields of the table: %s. Please contact the DBA to check the database and table information: %s",
                        queryDTO.getTableName(), e.getMessage()), e);
            }
        } finally {
            DBUtil.closeDBResources(pkRs, null, null);
            DBUtil.closeDBResources(fkRs, null, null);
            DBUtil.closeDBResources(uniqueRs, null, null);
            DBUtil.closeDBResources(allIndexRs, null, null);
            DBUtil.closeDBResources(rsColumn, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return newColumns;
    }


    @Override
    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        Statement statement = null;
        ResultSet rs = null;
        Map<String, String> columnComments = new HashMap<>();
        try {
            statement = sourceDTO.getConnection().createStatement();
            String queryColumnCommentSql =
                    "show full columns from " + transferSchemaAndTableName(sourceDTO, queryDTO);
            rs = statement.executeQuery(queryColumnCommentSql);
            while (rs.next()) {
                String columnName = rs.getString("Field");
                String columnComment = rs.getString("Comment");
                columnComments.put(columnName, columnComment);
            }

        } catch (Exception e) {
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the comment information of the field of the table: %s. Please contact the DBA to check the database and table information.",
                        queryDTO.getTableName()), e);
            }
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sourceDTO, clearStatus));
        }
        return columnComments;
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    protected String getCreateDatabaseSql(String dbName, String comment) {
        return String.format(CREATE_SCHEMA_SQL_TMPL, dbName);
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name is not empty");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(SHOW_DB_LIKE, dbName)).build()));
    }


    /**
     * 获取指定schema下的表，如果没有填schema，默认使用当前schema。支持正则匹配查询、条数限制
     *
     * @param sourceDTO 数据源信息
     * @param queryDTO  查询条件
     * @return
     */
    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        String schema = queryDTO.getSchema();
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            log.info("schema is empty，get current used schema!");
            // 获取当前数据库
            try {
                schema = getCurrentDatabase(sourceDTO);
            } catch (Exception e) {
                throw new DtLoaderException(String.format("get current used database error！,%s", e.getMessage()), e);
            }

        }
        log.info("current used schema：{}", schema);
        StringBuilder constr = new StringBuilder();
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            constr.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        List<String> tableType = Lists.newArrayList(BASE_TABLE);
        // 获取视图
        if (BooleanUtils.isTrue(queryDTO.getView())) {
            tableType.add(VIEW);
        }
        return String.format(SHOW_TABLE_BY_SCHEMA_SQL, schema, String.join(",", tableType), constr.toString());
    }

    /**
     * 处理 schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema
     * @param tableName
     * @return
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("`") || !tableName.endsWith("`")) {
            tableName = String.format("`%s`", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("`") || !schema.endsWith("`")) {
            schema = String.format("`%s`", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    protected Pair<Character, Character> getSpecialSign() {
        return Pair.of('`', '`');
    }

    /**
     * 获取表
     * @param sourceDTO 数据源描述对象，包含连接数据库所需的信息，如数据库类型、连接字符串等
     * @param queryDTO  SQL查询对象，包含查询数据库表所需的条件，如表名、schema名等
     * @return
     */
    @Override
    public List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> dbTableVOS = new ArrayList<>();
        try {
            statement = goldenDbSourceDTO.getConnection().createStatement();

            //判断搜查类型为空时查全部
            String sql = "";
            if (StringUtils.isEmpty(queryDTO.getType())) {
                sql = String.format(SqlConstants.GET_TABLE_SCHEMA_SQL_ALL, queryDTO.getDbName(), "");
            } else {
                String type = SqlConstants.BASE_TABLE;
                if ("VIEW".equalsIgnoreCase(queryDTO.getType())) {
                    type = SqlConstants.VIEW;
                }
                sql = String.format(SqlConstants.GET_TABLE_SCHEMA_SQL, queryDTO.getDbName(), type, "");
            }
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            //拼上分组
            sql=sql+SqlConstants.GROUP_SQL;
            log.info("getMedataDataTables sql:{}",sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO dbTableVO = new DbTableVO();
                dbTableVO.setDbName(resultSet.getString("TABLE_SCHEMA"));
                dbTableVO.setName(resultSet.getString("TABLE_NAME"));
                String tableType = resultSet.getString("table_type");
                if ("BASE TABLE".equalsIgnoreCase(tableType)) {
                    tableType = SqlConstants.TABLE_TYPE;
                }
                dbTableVO.setType(tableType);
                dbTableVOS.add(dbTableVO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取表或者视图异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(goldenDbSourceDTO, clearStatus));
        }
        return dbTableVOS;
    }

    /**
     * 获取索引信息
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    @Override
    public  List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = goldenDbSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //表名为空时  查所有
            if (StringUtils.isEmpty(queryDTO.getTableName())) {
                sql = String.format(SqlConstants.GET_INDEX_SQL, queryDTO.getDbName());
            } else {
                sql = String.format(SqlConstants.GET_INDEX_SQL_BY_TABLE, queryDTO.getDbName(), queryDTO.getTableName());
            }
            //是否有模糊搜索
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_INDEX_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            //拼上分组
            sql=sql+SqlConstants.GROUP_INDEX_SQL;
            log.info("getIndexList SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(resultSet.getString("TABLE_SCHEMA"));
                indexMetaDTO.setTableName(resultSet.getString("TABLE_NAME"));
                indexMetaDTO.setName(resultSet.getString("INDEX_NAME"));
                String indexType = resultSet.getString("INDEX_TYPE");
                indexMetaDTO.setIndexType(indexType);
                indexMetaDTO.setType(queryDTO.getType());
                indexMetaDTO.setComment(resultSet.getString("INDEX_COMMENT"));
                indexMetaDTO.setUnique(resultSet.getInt("is_unique"));
                indexMetaDTO.setColumnCount(resultSet.getInt("COLUMN_COUNT"));
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(goldenDbSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public   List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = goldenDbSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String  sql = String.format(SqlConstants.GET_INDEX_COLUMN_SQL, queryDTO.getDbName(), queryDTO.getTableName(),queryDTO.getIndexName());
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                indexMetaDTO.setKey(resultSet.getString("COLUMN_NAME"));
                indexMetaDTO.setColumnOrder(resultSet.getInt("SEQ_IN_INDEX"));
                indexMetaDTO.setCollation(resultSet.getString("collation"));
                String nullable = resultSet.getString("NULLABLE");
                //如果是yes  则可为空
                if ("YES".equalsIgnoreCase(nullable)) {
                    indexMetaDTO.setNotNullFlag(true);
                } else {
                    indexMetaDTO.setNotNullFlag(false);
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引字段异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(goldenDbSourceDTO, clearStatus));
        }
        return indexCols;
    }

    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = goldenDbSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //type 为函数还是存储过程
            if(SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())){
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getDbName(),SqlConstants.FUNCTION_TYPE);
            }

            if(SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())){
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getDbName(),SqlConstants.PROCEDURE_TYPE);
            }
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_PRODUCE_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }

            log.info("getFunctionList SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(resultSet.getString("ROUTINE_SCHEMA"));
                indexMetaDTO.setName(resultSet.getString("ROUTINE_NAME"));
                String indexType = resultSet.getString("ROUTINE_TYPE");
                indexMetaDTO.setType(indexType);
                indexMetaDTO.setComment(resultSet.getString("ROUTINE_COMMENT"));
                indexMetaDTO.setDdl(resultSet.getString("ROUTINE_DEFINITION"));
                indexMetaDTO.setCharset(resultSet.getString("CHARACTER_SET_CLIENT"));
                indexMetaDTO.setCollation(resultSet.getString("COLLATION_CONNECTION"));
                indexMetaDTO.setExternalLanguage(resultSet.getString("EXTERNAL_LANGUAGE"));
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(goldenDbSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = goldenDbSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String  sql = String.format(SqlConstants.GET_PRODUCE_ARGUMENTS_SQL, queryDTO.getDbName(),queryDTO.getObjectName()).toLowerCase();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                String parameterName = resultSet.getString("PARAMETER_NAME");
                if(StringUtils.isEmpty(parameterName)){
                    parameterName="RETURN";
                }
                indexMetaDTO.setKey(parameterName);
                indexMetaDTO.setType(resultSet.getString("DATA_TYPE"));
                String parameterMode = resultSet.getString("PARAMETER_MODE");
                if(StringUtils.isEmpty(parameterMode)){
                    parameterMode="RETURN";
                }
                indexMetaDTO.setInOut(parameterMode);
                indexMetaDTO.setColumnOrder(resultSet.getInt("ORDINAL_POSITION"));
                int characterOctetLength = resultSet.getInt("CHARACTER_OCTET_LENGTH");
                int numericPrecision = resultSet.getInt("NUMERIC_PRECISION");
                if(characterOctetLength>0){
                    indexMetaDTO.setLength(characterOctetLength);
                }else{
                    indexMetaDTO.setLength(numericPrecision);
                    indexMetaDTO.setPrecision(numericPrecision);
                    indexMetaDTO.setScale(resultSet.getInt("NUMERIC_SCALE"));
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(goldenDbSourceDTO, clearStatus));
        }
        return indexCols;
    }

    @Override
    public Integer getMaxConnections(ISourceDTO sourceDTO) {
        Integer result = null;
        try {
            List<Map<String, Object>> resultList = executeQuery(sourceDTO, SqlQueryDTO.builder().sql(SqlConstants.GET_MAX_CONNECTIONS).build());
            if (CollectionUtils.isNotEmpty(resultList)) {
                result = MapUtils.getInteger(resultList.get(0), "max_connections");
            }
        } catch (Exception e) {
            log.error("get max connections error", e);
        }
        return result;
    }
    @Override
    public Boolean getMetadataPrivileges(ISourceDTO sourceDTO) {
        Boolean result = null;
        GoldenDbSourceDTO goldenDbSourceDTO = (GoldenDbSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, SqlQueryDTO.builder().build(), false);
        try {
            result = checkViaGrants(goldenDbSourceDTO.getConnection()) || checkViaTestQuery(goldenDbSourceDTO.getConnection());
        } catch (Exception e) {
            // 异常为无权限
            result = false;
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(goldenDbSourceDTO, clearStatus));
        }
        return result;
    }

    private boolean checkViaGrants(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(SqlConstants.SHOW_GRANTS)) {
            Pattern globalSelectPattern = Pattern.compile(
                    "^GRANT .*\\bSELECT\\b.* ON \\*\\.\\* TO ",
                    Pattern.CASE_INSENSITIVE
            );
            while (rs.next()) {
                String grant = rs.getString(1);
                // 匹配是否存在全局 SELECT 权限
                if (globalSelectPattern.matcher(grant).find()) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkViaTestQuery(Connection conn) {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(SqlConstants.GET_METADATA_PRIVILEGES)) {
            // 查询成功 = 有权限
            return rs.next();
        } catch (SQLException e) {
            return false; // 查询失败 = 无权限
        }
    }

}
