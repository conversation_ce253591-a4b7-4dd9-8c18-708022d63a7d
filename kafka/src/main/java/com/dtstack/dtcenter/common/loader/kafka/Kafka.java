/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.kafka;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.*;
import com.dtstack.dtcenter.common.loader.common.exception.ErrorCode;
import com.dtstack.dtcenter.common.loader.kafka.util.KafkaUtil;
import com.dtstack.dtcenter.loader.client.IKafka;
import com.dtstack.dtcenter.loader.dto.KafkaConsumerDTO;
import com.dtstack.dtcenter.loader.dto.KafkaPartitionDTO;
import com.dtstack.dtcenter.loader.dto.KafkaTopicDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.KafkaSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * kafka 客户端，支持 kafka 0.10、10.11、1.x、2.x 版本
 * 支持 kafka kerberos认证(SASL/GSSAPI)、用户名密码认证(SASL/PLAIN)
 *
 * <AUTHOR>
 * date：Created in 下午4:39 2021/7/9
 * company: www.dtstack.com
 */
public class Kafka<T> implements IKafka<T> {
    @Override
    public Boolean testCon(ISourceDTO sourceDTO) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        return KafkaUtil.checkConnection(kafkaSourceDTO);
    }

    @Override
    public String getAllBrokersAddress(ISourceDTO sourceDTO) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        if (StringUtils.isNotBlank(kafkaSourceDTO.getBrokerUrls())) {
            return kafkaSourceDTO.getBrokerUrls();
        }
        return KafkaUtil.getAllBrokersAddressFromZk(kafkaSourceDTO.getUrl());
    }

    @Override
    public List<BrokersDTO> getAllBrokersInfoList(ISourceDTO source) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        List<BrokersDTO> brokerInfoList = KafkaUtil.getBrokerInfoList(kafkaSourceDTO.getUrl());
        return brokerInfoList;
    }

    @Override
    public List<String> getTopicList(ISourceDTO sourceDTO) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        List<String> topics = KafkaUtil.getTopicList(kafkaSourceDTO);
        // 过滤掉存储消费者组 offset 的 topic
        return topics;
    }

    @Override
    public List<TopIcInfoDTO> getTopicInfoList(ISourceDTO source) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        List<TopIcInfoDTO> topicInfoList = KafkaUtil.getTopicInfoList((kafkaSourceDTO));
        return topicInfoList;
    }


    @Override
    public List getOffset(ISourceDTO sourceDTO, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        return KafkaUtil.getPartitionOffset(kafkaSourceDTO, topic);
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return getPreview(sourceDTO, queryDTO, KafkaUtil.EARLIEST);
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO sourceDTO, SqlQueryDTO queryDTO, String prevMode) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        List<String> recordsFromKafka = KafkaUtil.getRecordsFromKafka(kafkaSourceDTO, queryDTO.getTableName(), prevMode);
        List<Object> records = new ArrayList<>(recordsFromKafka);
        List<List<Object>> result = new ArrayList<>();
        result.add(records);
        return result;
    }

    @Override
    public List<List<Object>> getRecordsFromKafkaByStatistics(ISourceDTO sourceDTO, Integer partition, String topic, String autoReset, String maxpoolrecords) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        List<JSONObject> recordsFromKafkaByStatistics = KafkaUtil.getRecordsFromKafkaByStatistics(kafkaSourceDTO, partition, topic, autoReset, maxpoolrecords);
        List<Object> records = new ArrayList<>(recordsFromKafkaByStatistics);
        List<List<Object>> result = new ArrayList<>();
        result.add(records);
        return result;
    }

    @Override
    public List<KafkaPartitionDTO> getTopicPartitions(ISourceDTO source, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        return KafkaUtil.getPartitions(kafkaSourceDTO, topic);
    }

    @Override
    public List<String> consumeData(ISourceDTO source, String topic, Integer collectNum, String offsetReset, Long timestampOffset, Integer maxTimeWait) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        return KafkaUtil.consumeData(kafkaSourceDTO, topic, collectNum, offsetReset, timestampOffset, maxTimeWait);
    }

    @Override
    public List<String> listConsumerGroup(ISourceDTO source) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        return KafkaUtil.listConsumerGroup(kafkaSourceDTO, null);
    }

    @Override
    public List<ConsumerInfoDTO> getConsumerInfoList(ISourceDTO source) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        List<ConsumerInfoDTO> consumerGroupInfoDTOS = KafkaUtil.listConsumerInfo(kafkaSourceDTO);
        return consumerGroupInfoDTOS;
    }

    @Override
    public JSONObject listTopicInfoByGroupId(ISourceDTO sourceDTO, String groupId) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        JSONObject objects = KafkaUtil.listTopicInfoByGroupId(kafkaSourceDTO, groupId);
        return objects;
    }


    @Override
    public List<String> listConsumerGroupByTopic(ISourceDTO source, String topic) {
        if (StringUtils.isBlank(topic)) {
            throw new DtLoaderException("topic cannot be empty...");
        }
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        return KafkaUtil.listConsumerGroup(kafkaSourceDTO, topic);
    }

    @Override
    public List<KafkaConsumerDTO> getGroupInfoByGroupId(ISourceDTO source, String groupId) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        return KafkaUtil.getGroupInfoByGroupId(kafkaSourceDTO, groupId, null);
    }

    @Override
    public List<KafkaConsumerDTO> getGroupInfoByTopic(ISourceDTO source, String topic) {
        if (StringUtils.isBlank(topic)) {
            throw new DtLoaderException("topic cannot be empty...");
        }
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        return KafkaUtil.getGroupInfoByGroupId(kafkaSourceDTO, null, topic);
    }

    @Override
    public List<KafkaConsumerDTO> getGroupInfoByGroupIdAndTopic(ISourceDTO source, String groupId, String topic) {
        if (StringUtils.isBlank(topic)) {
            throw new DtLoaderException("topic cannot be empty...");
        }
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        return KafkaUtil.getGroupInfoByGroupId(kafkaSourceDTO, groupId, topic);
    }

    @Override
    public Integer getBrokerLeaderSkewed(ISourceDTO source, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        Integer brokerLeaderSkewed = KafkaUtil.getBrokerLeaderSkewed(kafkaSourceDTO, topic);
        return brokerLeaderSkewed;
    }

    @Override
    public Integer getBrokerSkewed(ISourceDTO source, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        Integer brokerSkewed = KafkaUtil.getBrokerSkewed(kafkaSourceDTO, topic);
        return brokerSkewed;
    }

    @Override
    public Integer getBrokerSpread(ISourceDTO source, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        Integer brokerSpread = KafkaUtil.getBrokerSpread(kafkaSourceDTO, topic);
        return brokerSpread;
    }

    @Override
    public String getBrokerKafkaVersion(String host, Integer port, String id) {
        try {

            String brokerKafkaVersion = KafkaUtil.getBrokerKafkaVersion(host, port, id);
            return brokerKafkaVersion;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<TopIcMetaInfoDTO> getTopIcMetaByTopIc(ISourceDTO source, String topIcName) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        List<TopIcMetaInfoDTO> topIcMetaByTopIc = KafkaUtil.getTopIcMetaByTopIc(kafkaSourceDTO, topIcName);
        return topIcMetaByTopIc;
    }

    @Override
    public String getBrokerCpuUse(ISourceDTO source, String host, Integer port) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        String brokerCpuUse = KafkaUtil.getBrokerCpuUse(host, port);
        return brokerCpuUse;
    }

    @Override
    public String getBrokerMemoryUse(ISourceDTO source, String host, Integer port) {
        String brokerMemoryUse = KafkaUtil.getBrokerMemoryUse(host, port);
        return brokerMemoryUse;
    }

    @Override
    public String getBrokerMemoryUsePercent(ISourceDTO source, String host, Integer port) {
        String brokerMemoryUse = KafkaUtil.getBrokerMemoryUsePercent(host, port);
        return brokerMemoryUse;
    }

    @Override
    public Map<String, KafkaMonitorDTO> getTopicMonitor(ISourceDTO source, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        Map<String, KafkaMonitorDTO> topicMonitor = KafkaUtil.getTopicMonitor(kafkaSourceDTO.getUrl(), topic);
        return topicMonitor;
    }

    @Override
    public Map<String, KafkaMonitorDTO> getOnlineAllBrokersMBean(String host, Integer port) {
        Map<String, KafkaMonitorDTO> onlineAllBrokersMBean = KafkaUtil.getOnlineAllBrokersMBean(host, port);
        return onlineAllBrokersMBean;
    }

    @Override
    public double getCpuUsed(ISourceDTO source) {
        double cpuUsed = KafkaUtil.getCpuUsed(((KafkaSourceDTO) source).getUrl());
        return cpuUsed;
    }

    @Override
    public double getOSMemory(ISourceDTO source) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        double osMemory = KafkaUtil.getOSMemory(kafkaSourceDTO.getUrl());
        return osMemory;
    }

    @Override
    public Boolean createTopicFromBroker(ISourceDTO source, KafkaTopicDTO kafkaTopicDTO) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) source;
        return KafkaUtil.createTopicFromBroker(kafkaSourceDTO, kafkaTopicDTO);

    }

    @Override
    public Boolean createTopicPartitions(ISourceDTO sourceDTO, String topicName, Integer partitions) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        return KafkaUtil.createTopicPartitions(kafkaSourceDTO, topicName, partitions);
    }

    @Override
    public Boolean deleteTOpic(ISourceDTO sourceDTO, String topicName) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        return KafkaUtil.deleteTOpic(kafkaSourceDTO, topicName);
    }

    @Override
    public List<KpiInfoDTO> getBrokerKpiInfos(ISourceDTO sourceDTO) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        return KafkaUtil.getBrokerKpiInfos(kafkaSourceDTO.getUrl());
    }

    @Override
    public long getLogSizeByTopic(ISourceDTO sourceDTO, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        long logSizeByTopic = KafkaUtil.getLogSizeByTopic(kafkaSourceDTO, topic);
        return logSizeByTopic;
    }

    @Override
    public long getCapacityByTopic(ISourceDTO sourceDTO, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        long capacityByTopic = KafkaUtil.getCapacityByTopic(kafkaSourceDTO, topic);
        return capacityByTopic;
    }

    @Override
    public long getByteInByTopic(ISourceDTO sourceDTO, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        long byteInByTopic = KafkaUtil.getByteInByTopic(kafkaSourceDTO, topic);
        return byteInByTopic;
    }

    @Override
    public long getByteOutByTopic(ISourceDTO sourceDTO, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        long byteOutByTopic = KafkaUtil.getByteOutByTopic(kafkaSourceDTO, topic);
        return byteOutByTopic;
    }

    @Override
    public List<OffsetInfoDTO> getTopicOffset(ISourceDTO sourceDTO, String topicName, String groupName) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        List<OffsetInfoDTO> topicOffset = KafkaUtil.getTopicOffset(kafkaSourceDTO, topicName, groupName);
        return topicOffset;
    }

    @Override
    public long getKafkaProducerLogSizeByTopic(ISourceDTO sourceDTO, String topic) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        long kafkaProducerLogSizeByTopic = KafkaUtil.getKafkaProducerLogSizeByTopic(kafkaSourceDTO, topic);
        return kafkaProducerLogSizeByTopic;
    }

    @Override
    public String getConsumerStatus(ISourceDTO sourceDTO, String groupId) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        String consumerStatus = KafkaUtil.getConsumerStatus(kafkaSourceDTO, groupId);
        return consumerStatus;
    }

    @Override
    public OffsetInfoDTO getTopicMessageStatistics(ISourceDTO sourceDTO, String topic, String groupName) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        OffsetInfoDTO topicMessageStatistics = KafkaUtil.getTopicMessageStatistics(kafkaSourceDTO, topic, groupName);
        return topicMessageStatistics;
    }

    @Override
    public JSONObject getAllActiveTopicsTree(ISourceDTO sourceDTO) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        JSONObject jsonObject = new JSONObject();
        JSONObject allActiveTopicsTree = KafkaUtil.getAllActiveTopicsTree(kafkaSourceDTO, true);
        jsonObject.put("ActiveTopic",allActiveTopicsTree);
        JSONObject allActiveTopicsTree1 = KafkaUtil.getAllActiveTopicsTree(kafkaSourceDTO, false);
        jsonObject.put("ActiveTopicConsumers",allActiveTopicsTree1);

        return jsonObject;
    }


    @Override
    public List<T> getAllPartitions(ISourceDTO source, String topic) {
        List<KafkaPartitionDTO> partitions = KafkaUtil.getPartitions((KafkaSourceDTO) source, topic);
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }
}
