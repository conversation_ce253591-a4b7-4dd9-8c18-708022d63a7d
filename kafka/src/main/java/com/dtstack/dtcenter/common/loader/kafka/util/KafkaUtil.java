/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.kafka.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dsg.database.datasource.dto.*;
import com.dsg.database.datasource.enums.KafkaIndicatorName;
import com.dsg.database.datasource.utils.CleanSystemPropertiesUtils;
import com.dsg.database.datasource.utils.DateUtils;
import com.dsg.database.datasource.utils.NetUtils;
import com.dtstack.dtcenter.common.loader.common.utils.DateUtil;
import com.dtstack.dtcenter.common.loader.common.utils.TelUtil;
import com.dtstack.dtcenter.common.loader.kafka.KafkaConsistent;
import com.dtstack.dtcenter.common.loader.kafka.enums.EConsumeType;
import com.dtstack.dtcenter.loader.dto.KafkaConsumerDTO;
import com.dtstack.dtcenter.loader.dto.KafkaOffsetDTO;
import com.dtstack.dtcenter.loader.dto.KafkaPartitionDTO;
import com.dtstack.dtcenter.loader.dto.KafkaTopicDTO;
import com.dtstack.dtcenter.loader.dto.source.KafkaSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import kafka.admin.AdminClient;
import kafka.coordinator.group.GroupOverview;
import kafka.utils.ZkUtils;
import lombok.extern.slf4j.Slf4j;
import org.I0Itec.zkclient.ZkClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndTimestamp;
import org.apache.kafka.common.Node;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.security.JaasUtils;
import org.apache.zookeeper.data.Stat;
import scala.Option;
import scala.Tuple2;
import scala.collection.JavaConversions;
import scala.collection.Seq;
import sun.security.krb5.Config;

import javax.management.MBeanServerConnection;
import javax.management.ObjectName;
import javax.management.remote.JMXConnector;
import javax.security.auth.login.AppConfigurationEntry;
import javax.security.auth.login.Configuration;
import java.io.File;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 22:46 2020/2/26
 * @Description：Kafka 工具类
 */
@Slf4j
public class KafkaUtil {

    public static final String EARLIEST = "earliest";
    private static final int MAX_POOL_RECORDS = 5;
    private static final String BROKER_IDS_PATH = "/brokers/ids/";
    // 开启 kerberos 默认 sasl.kerberos.service.name
    private static final String DEFAULT_KERBEROS_NAME = "";
    private static final String DEFAULT_EFAK_JMX_URI = "service:jmx:rmi:///jndi/rmi://%s:%d/jmxrmi";
    private static final String TOPIC_CONCAT_CHARACTER = ",topic=";
    public static final String CONSUMER_OFFSET_TOPIC = "__consumer_offsets";
    private static final String BROKER_TOPICS_PATH = "/brokers/topics";
    private static final String KAFKA_LOG_SIZE = "kafka.log:type=Log,name=Size,topic=%s,partition=%s";

    /**
     * 写入 kafka jaas文件到 keytab 文件所在同一级目录，同时设置 krb5.conf 绝对路径到系统变量中
     *
     * @param kerberosConfig kafka kerberos 配置
     * @return jaas文件绝对路径
     */
    public static String writeKafkaJaas(Map<String, Object> kerberosConfig) {
        log.info("Initialize Kafka JAAS file, kerberosConfig : {}", kerberosConfig);
        if (MapUtils.isEmpty(kerberosConfig)) {
            return null;
        }

        // 处理 krb5.conf
        if (kerberosConfig.containsKey(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF)) {
            System.setProperty(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF, MapUtils.getString(kerberosConfig, HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF));
        }

        String keytabConf = MapUtils.getString(kerberosConfig, HadoopConfTool.PRINCIPAL_FILE);
        // 兼容历史数据
        keytabConf = StringUtils.isBlank(keytabConf) ? MapUtils.getString(kerberosConfig, HadoopConfTool.KAFKA_KERBEROS_KEYTAB) : keytabConf;
        try {
            File file = new File(keytabConf);
            File jaas = new File(file.getParent() + File.separator + "kafka_jaas.conf");
            if (jaas.exists()) {
                boolean checkDelete = jaas.delete();
                if (!checkDelete) {
                    log.error("delete file [{}] fail....", jaas.getAbsolutePath());
                }
            }

            String principal = MapUtils.getString(kerberosConfig, HadoopConfTool.PRINCIPAL);
            // 历史数据兼容
            principal = StringUtils.isBlank(principal) ? MapUtils.getString(kerberosConfig, "kafka.kerberos.principal") : principal;
            FileUtils.write(jaas, String.format(KafkaConsistent.KAFKA_JAAS_CONTENT, keytabConf, principal));
            String kafkaLoginConf = jaas.getAbsolutePath();
            log.info("Init Kafka Kerberos:login-conf:{}\n --sasl.kerberos.service.name:{}", keytabConf, principal);
            return kafkaLoginConf;
        } catch (IOException e) {
            throw new DtLoaderException(String.format("Writing to Kafka configuration file exception,%s", e.getMessage()), e);
        }
    }


    /**
     * 从 ZK 中获取所有的 Kafka broker 地址
     *
     * @param zkUrls zk 地址
     * @return kafka broker 地址
     */
    public static String getAllBrokersAddressFromZk(String zkUrls) {
        log.info("Obtain Kafka Broker address through ZK : {}", zkUrls);
        ZkUtils zkUtils = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            if (StringUtils.isBlank(zkUrls) || !TelUtil.checkTelnetAddr(zkUrls)) {
                throw new DtLoaderException("Please configure the correct zookeeper address");
            }

            zkUtils = ZkUtils.apply(zkUrls, KafkaConsistent.SESSION_TIME_OUT,
                    KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
            List<BrokersDTO> brokersByZk = getBrokersByZk(zkUtils);
            if (CollectionUtils.isNotEmpty(brokersByZk)) {
                for (int i = 0; i < brokersByZk.size(); i++) {
                    BrokersDTO broker = brokersByZk.get(i);
                    if (i != brokersByZk.size() - 1) {
                        stringBuilder.append(broker.getHost()).append(":").append(broker.getPort()).append(",");
                    } else {
                        stringBuilder.append(broker.getHost()).append(":").append(broker.getPort());
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        } finally {
            if (Objects.nonNull(zkUtils)) {
                zkUtils.close();
            }
        }
        return stringBuilder.toString();
    }

    public static List<BrokersDTO> getBrokerInfoList(String zkUrls) {
        log.info("Obtain Kafka Broker address through ZK : {}", zkUrls);
        if (StringUtils.isBlank(zkUrls) || !TelUtil.checkTelnetAddr(zkUrls)) {
            throw new DtLoaderException("Please configure the correct zookeeper address");
        }

        ZkUtils zkUtils = null;
        List<BrokersDTO> result = new ArrayList<>();
        try {
            zkUtils = ZkUtils.apply(zkUrls, KafkaConsistent.SESSION_TIME_OUT,
                    KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
            result = getBrokersByZk(zkUtils);


        } finally {
            if (Objects.nonNull(zkUtils)) {
                zkUtils.close();
            }
        }
        return result;
    }


    /**
     * 从 KAFKA 中获取 TOPIC 的信息
     *
     * @param kafkaSourceDTO kafka 数据源信息
     * @return topic 列表
     */
    public static List<String> getTopicList(KafkaSourceDTO kafkaSourceDTO) {
        Properties defaultKafkaConfig = initProperties(kafkaSourceDTO);
        List<String> results = Lists.newArrayList();
        KafkaConsumer<String, String> consumer=null;
        try  {
            consumer= new KafkaConsumer<>(defaultKafkaConfig);
            Map<String, List<PartitionInfo>> topics = consumer.listTopics();
            consumer.close();
            if (topics != null) {
                results.addAll(topics.keySet());
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("failed to get topics from broker. %s", e.getMessage()), e);
        } finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            destroyProperty();
        }
        return results;
    }

    public static List<TopIcInfoDTO> getTopicInfoList(KafkaSourceDTO kafkaSourceDTO) {
        Properties defaultKafkaConfig = initProperties(kafkaSourceDTO);
        List<TopIcInfoDTO> results = Lists.newArrayList();
        ZkUtils zkUtils = null;
        zkUtils = ZkUtils.apply(kafkaSourceDTO.getUrl(), KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        ZkClient zkClient = zkUtils.zkClient();
        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(defaultKafkaConfig)) {
            Map<String, List<PartitionInfo>> topics = consumer.listTopics();

            if (topics != null) {
                Set<String> keySet = topics.keySet();
                for (String s : keySet) {
                    Stat stat = new Stat();
                    //获取topic的创建时间和修改时间
                    String test = zkUtils.getTopicPath(s);
                    Object o = zkClient.readData(test, stat);
                    //存放top信息
                    TopIcInfoDTO build = TopIcInfoDTO.builder()
                            .topicName(s)
                            .partitionNum(topics.get(s).size())
                            .replicaNum(topics.get(s).get(0).replicas().length)
                            .updateTime(stat.getMtime())
                            .createTime(stat.getCtime())
                            .build();
                    results.add(build);

                }
                return results;

            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new DtLoaderException(String.format("failed to get topics from broker. %s", e.getMessage()), e);
        } finally {
            if(Objects.nonNull(zkUtils)){
                zkUtils.close();
            }
            if(zkClient!=null){
                zkClient.close();
            }
            destroyProperty();
        }
        return null;
    }


//    public static List


    public static Integer getBrokerSpread(KafkaSourceDTO kafkaSourceDTO, String topicName) {
        ZkUtils zkUtils = ZkUtils.apply(kafkaSourceDTO.getUrl(), KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());

        int spread = 0;
        try {
            List<BrokersDTO> brokers = getBrokersByZk(zkUtils);
            List<KafkaPartitionDTO> topIcName = getPartitions(kafkaSourceDTO, topicName);
            //获取broker使用率
            Set<Integer> brokerSizes = new HashSet<>();
            for (KafkaPartitionDTO kafkaPartitionDTO : topIcName) {
                System.out.println(kafkaPartitionDTO.getReplicas());
                Integer integer = Integer.valueOf(kafkaPartitionDTO.getReplicas().length);
                brokerSizes.add(integer);
            }
            spread = brokerSizes.size() * 100 / brokers.size();
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if(Objects.nonNull(zkUtils)){
                zkUtils.close();
            }
        }

        return spread;
    }

    public static Integer getBrokerSkewed(KafkaSourceDTO kafkaSourceDTO, String topicName) {
        int skewed = 0;
        int partitionAndReplicaTopics = 0;
        Set<Integer> brokerSizes = new HashSet<>();
        Map<Integer, Integer> brokers = new HashMap<>();
        List<KafkaPartitionDTO> topIcName = getPartitions(kafkaSourceDTO, topicName);

        //获取broker使用率
        for (KafkaPartitionDTO kafkaPartitionDTO : topIcName) {
            List<Integer> replicasIntegers = new ArrayList<>();
            try {
                for (KafkaPartitionDTO.Node replica : kafkaPartitionDTO.getReplicas()) {
                    replicasIntegers.add(replica.getId());
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
            brokerSizes.addAll(replicasIntegers);
            partitionAndReplicaTopics += replicasIntegers.size();
            for (Integer brokerId : replicasIntegers) {
                if (brokers.containsKey(brokerId)) {
                    int value = brokers.get(brokerId);
                    brokers.put(brokerId, value + 1);
                } else {
                    brokers.put(brokerId, 1);
                }
            }
        }
        int brokerSize = brokerSizes.size();
        int normalSkewedValue = (partitionAndReplicaTopics % brokerSize == 0) ? (partitionAndReplicaTopics / brokerSize) : (partitionAndReplicaTopics / brokerSize + 1);
        int brokerSkewSize = 0;
        for (Map.Entry<Integer, Integer> entry : brokers.entrySet()) {
            if (entry.getValue() > normalSkewedValue) {
                brokerSkewSize++;
            }
        }
        skewed = brokerSkewSize * 100 / brokerSize;
        return skewed;
    }

    public static Integer getBrokerLeaderSkewed(KafkaSourceDTO kafkaSourceDTO, String topicName) {
        int leaderSkewed = 0;
        Map<Integer, Integer> brokerLeaders = new HashMap<>();
        Set<Integer> brokerSizes = new HashSet<>();
        List<KafkaPartitionDTO> topIcName = getPartitions(kafkaSourceDTO, topicName);
        for (KafkaPartitionDTO kafkaPartitionDTO : topIcName) {
            List<Integer> replicasIntegers = new ArrayList<>();
            try {
                replicasIntegers.add(Integer.valueOf(kafkaPartitionDTO.getReplicas().length));
            } catch (Exception e) {
                e.printStackTrace();
            }
            brokerSizes.addAll(replicasIntegers);
            if (brokerLeaders.containsKey(kafkaPartitionDTO.getLeader().getId())) {
                int value = brokerLeaders.get(kafkaPartitionDTO.getLeader().getId());
                brokerLeaders.put(kafkaPartitionDTO.getLeader().getId(), value + 1);
            } else {
                brokerLeaders.put(kafkaPartitionDTO.getLeader().getId(), 1);
            }
        }
        int brokerSize = brokerSizes.size();
        int brokerSkewLeaderNormal = (topIcName.size() % brokerSize == 0) ? (topIcName.size() / brokerSize) : (topIcName.size() / brokerSize + 1);
        int brokerSkewLeaderSize = 0;
        for (Map.Entry<Integer, Integer> entry : brokerLeaders.entrySet()) {
            if (entry.getValue() > brokerSkewLeaderNormal) {
                brokerSkewLeaderSize++;
            }
        }
        leaderSkewed = brokerSkewLeaderSize * 100 / brokerSize;
        return leaderSkewed;
    }


    public static List<BrokersDTO> getBrokersByZk(ZkUtils zkUtils) {
        List<BrokersDTO> result = new ArrayList<>();
        List<Object> sortedBrokerList = JavaConversions.seqAsJavaList(zkUtils.getSortedBrokerList());
        try {
            for (Object o1 : sortedBrokerList) {
                System.out.println(o1);
                Tuple2<Option<String>, Stat> tuple = zkUtils.readDataMaybeNull(BROKER_IDS_PATH + o1);
                BrokersDTO broker = new BrokersDTO();
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                broker.setCreated(df.format(new Date(tuple._2.getCtime())));
                broker.setModify(df.format(new Date(tuple._2.getMtime())));
                String tupleString = new String(tuple._1.get());
                System.out.println(tupleString);
                String host = JSON.parseObject(tupleString).getString("host");
                String hostAddress="";
                int port = 0;
                if (StringUtils.isEmpty(host)) {
                    String endpoints = JSON.parseObject(tupleString).getString("endpoints");
                    List<String> endpointsList = JSON.parseArray(endpoints, String.class);

                    if (endpointsList.size() >= 1) {
                        for (String endpointsStr : endpointsList) {
                            String tmp = endpointsStr.split("//")[1];
                            host = tmp.split(":")[0];
                            hostAddress = InetAddress.getByName(host).getHostAddress();
                            host = hostAddress;
                            port = Integer.parseInt(tmp.split(":")[1]);
                            break;
                        }
                    }
                } else {
                     hostAddress = InetAddress.getByName(host).getHostAddress();
                    host = hostAddress;
                    port = JSON.parseObject(tupleString).getInteger("port");
                }

                broker.setHost(host);
                broker.setPort(port);
                broker.setJmxPort(JSON.parseObject(tupleString).getInteger("jmx_port"));
                broker.setJmxPortStatus(broker.getHost().equals("-1") ? false : NetUtils.telnet(broker.getHost(), broker.getJmxPort()));
                broker.setId((Integer) o1);
                result.add(broker);
            }

        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }finally {
            if (zkUtils != null) {
                zkUtils.close();
            }
        }

        return result;
    }


    /**
     * 通过 KAFKA 中创建 TOPIC 的信息
     *
     * @param sourceDTO 数据源信息
     */
    public static Boolean createTopicFromBroker(KafkaSourceDTO sourceDTO, KafkaTopicDTO kafkaTopicDTO) {
        Properties defaultKafkaConfig = initProperties(sourceDTO);
        org.apache.kafka.clients.admin.AdminClient client = org.apache.kafka.clients.admin.AdminClient.create(defaultKafkaConfig);

        // 获取kafka client
        try {
            NewTopic topic = new NewTopic(kafkaTopicDTO.getTopicName(), kafkaTopicDTO.getPartitions(), kafkaTopicDTO.getReplicationFactor());
            client.createTopics(Collections.singleton(topic));
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            client.close();
        }
        return true;
    }

    /**
     * 通过 KAFKA 中创建topic的分区 的信息
     *
     * @param sourceDTO  数据源信息
     * @param topicName  topic 名称
     * @param partitions 分区数量
     */
    public static Boolean createTopicPartitions(KafkaSourceDTO sourceDTO, String topicName,
                                                Integer partitions) {
        Properties defaultKafkaConfig = initProperties(sourceDTO);
        AdminClient client = AdminClient.create(defaultKafkaConfig);
        int existPartitions = (int) partitionNumbers(sourceDTO.getUrl(), topicName);
        // 获取kafka client
        try {
          /*  Map<String, NewPartitions> newPartitions = new HashMap<String, NewPartitions>();
            newPartitions.put(topicName, NewPartitions.increaseTo(existPartitions + partitions));
            client.createPartitions(newPartitions);*/
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        } finally {
            client.close();
        }
        return true;
    }

    public static long partitionNumbers(String zkurls, String topic) {
        long count = 0L;
        if (CONSUMER_OFFSET_TOPIC.equals(topic)) {
            return count;
        }
        ZkUtils zkUtils = ZkUtils.apply(zkurls, KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        try {
            if (zkUtils.pathExists(BROKER_TOPICS_PATH + "/" + topic + "/partitions")) {
                Seq<String> subBrokerTopicsPaths = zkUtils.getChildren(BROKER_TOPICS_PATH + "/" + topic + "/partitions");
                count = JavaConversions.seqAsJavaList(subBrokerTopicsPaths).size();
            }
        } catch (Exception e) {
            log.error("Get topic partition numbers has error, msg is " + e.getCause().getMessage());
            e.printStackTrace();
        }
        if (zkUtils != null) {
            zkUtils.close();
        }
        return count;
    }


    public static Boolean deleteTOpic(KafkaSourceDTO sourceDTO, String topicName) {
        Properties defaultKafkaConfig = initProperties(sourceDTO);
        org.apache.kafka.clients.admin.AdminClient client = org.apache.kafka.clients.admin.AdminClient.create(defaultKafkaConfig);
        // 获取kafka client
        try {
            client.deleteTopics(Collections.singleton(topicName)).all().get();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            client.close();
        }
        return true;
    }

    /**
     * 获取所有分区中最大最小的偏移量
     *
     * @param sourceDTO 数据源信息
     * @param topic     kafka topic
     * @return kafka 每个分区的最大最小 offset
     */
    public static List<KafkaOffsetDTO> getPartitionOffset(KafkaSourceDTO sourceDTO, String topic) {
        Properties defaultKafkaConfig = initProperties(sourceDTO);
        KafkaConsumer<String, String> consumer=null;
        try {
            consumer = new KafkaConsumer<>(defaultKafkaConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            List<PartitionInfo> allPartitionInfo = consumer.partitionsFor(topic);
            for (PartitionInfo partitionInfo : allPartitionInfo) {
                partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
            }

            Map<Integer, KafkaOffsetDTO> kafkaOffsetDTOMap = new HashMap<>();
            Map<TopicPartition, Long> beginningOffsets = consumer.beginningOffsets(partitions);
            for (Map.Entry<TopicPartition, Long> entry : beginningOffsets.entrySet()) {
                KafkaOffsetDTO offsetDTO = new KafkaOffsetDTO();
                offsetDTO.setPartition(entry.getKey().partition());
                offsetDTO.setFirstOffset(entry.getValue());
                offsetDTO.setLastOffset(entry.getValue());
                kafkaOffsetDTOMap.put(entry.getKey().partition(), offsetDTO);
            }

            Map<TopicPartition, Long> endOffsets = consumer.endOffsets(partitions);
            for (Map.Entry<TopicPartition, Long> entry : endOffsets.entrySet()) {
                KafkaOffsetDTO offsetDTO = kafkaOffsetDTOMap.getOrDefault(entry.getKey().partition(),
                        new KafkaOffsetDTO());
                offsetDTO.setPartition(entry.getKey().partition());
                offsetDTO.setFirstOffset(null == offsetDTO.getFirstOffset() ? entry.getValue() :
                        offsetDTO.getFirstOffset());
                offsetDTO.setLastOffset(entry.getValue());
                kafkaOffsetDTOMap.put(entry.getKey().partition(), offsetDTO);
            }

            return new ArrayList<>(kafkaOffsetDTOMap.values());
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            destroyProperty();
        }
    }

    public static long getKafkaProducerLogSizeByTopic(KafkaSourceDTO sourceDTO, String topic) {
        long producerLogSize = 0L;
        Properties defaultKafkaConfig = initProperties(sourceDTO);
        KafkaConsumer<String, String> consumer=null;
        try {
            consumer= new KafkaConsumer<>(defaultKafkaConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            List<PartitionInfo> allPartitionInfo = consumer.partitionsFor(topic);
            for (PartitionInfo partitionInfo : allPartitionInfo) {
                partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
            }

            Map<TopicPartition, Long> endOffsets = consumer.endOffsets(partitions);
            for (Map.Entry<TopicPartition, Long> entry : endOffsets.entrySet()) {
                producerLogSize += entry.getValue();
            }


        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            destroyProperty();
        }
        return producerLogSize;
    }

    /**
     * 根据 Kafka 地址 校验连接性
     *
     * @param sourceDTO 数据源信息
     * @return 是否连通
     */
    public static boolean checkConnection(KafkaSourceDTO sourceDTO) {
        Properties props = initProperties(sourceDTO);

        /* 定义consumer */
        KafkaConsumer<String, String> consumer=null;
        try {
            consumer= new KafkaConsumer<>(props);
            Map<String, List<PartitionInfo>> stringListMap = consumer.listTopics();
        } catch (Exception e) {
            throw new DtLoaderException(String.format("connect kafka fail: %s", e.getMessage()), e);
        } finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            destroyProperty();
        }
//        if (StringUtils.isNotEmpty(sourceDTO.getBrokerUrls())) {
//            String allBrokersAddressFromZk = getAllBrokersAddressFromZk(sourceDTO.getUrl());
//            String[] split = allBrokersAddressFromZk.split(",");
//            String[] split1 = sourceDTO.getBrokerUrls().split(",");
//            for (String s : split1) {
//                if (!Arrays.asList(split).contains(s)) {
//                    return false;
//                }
//            }
//
//
//        }

        return true;
    }

    private static void destroyProperty() {
        System.clearProperty("java.security.auth.login.config");
        System.clearProperty("javax.security.auth.useSubjectCredsOnly");
    }

    /**
     * 获取 kafka broker 地址，如果 broker 填写为空则从 zookeeper 中获取
     *
     * @param sourceDTO kafka 数据源信息
     * @return kafka broker 地址
     */
    private static String getKafkaBroker(KafkaSourceDTO sourceDTO) {
        String allBrokersAddressFromZk = getAllBrokersAddressFromZk(sourceDTO.getUrl());
        String[] split = allBrokersAddressFromZk.split(",");
        String brokerUrls = StringUtils.isEmpty(sourceDTO.getBrokerUrls()) ? allBrokersAddressFromZk : sourceDTO.getBrokerUrls();
        String[] split1 = brokerUrls.split(",");
        if (StringUtils.isBlank(brokerUrls) || split.length != split1.length) {
            throw new DtLoaderException("failed to get broker from zookeeper"+"brokers from zk"+allBrokersAddressFromZk+ "input broker"+brokerUrls);
        }

        return brokerUrls;
    }

    /**
     * 初始化 Kafka 配置信息
     *
     * @param sourceDTO 数据源信息
     * @return kafka 配置
     */
    private synchronized static Properties initProperties(KafkaSourceDTO sourceDTO) {
        //先清除上一次的环境变量
        CleanSystemPropertiesUtils.cleanZk();
        CleanSystemPropertiesUtils.cleanJava();

        Properties props = new Properties();
        /* 是否自动确认offset */
        props.put("enable.auto.commit", "true");
        /* 设置group id */
        props.put("group.id", KafkaConsistent.KAFKA_GROUP);
        /* 自动确认offset的时间间隔 */
        props.put("auto.commit.interval.ms", "1000");
        //heart beat 默认3s
        props.put("session.timeout.ms", "10000");
        //一次性的最大拉取条数
        props.put("max.poll.records", "5");
        props.put("auto.offset.reset", "earliest");
        /* key的序列化类 */
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        /* value的序列化类 */
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

        /*设置超时时间*/
//        props.put("request.timeout.ms", "10500");
        props.put("request.timeout.ms", "30000");
        //元数据更新周期设置为 10 秒
        props.put("metadata.max.age.ms", "10000");
         //增加连接空闲时间
        props.put("connections.max.idle.ms", "60000");


        // username 和 password 都为空的时候走 SASL/PLAIN 认证逻辑
        if (StringUtils.isNotBlank(sourceDTO.getUsername()) && StringUtils.isNotBlank(sourceDTO.getPassword())) {
            // SASL/PLAIN 相关设置
            props.put("security.protocol", "SASL_PLAINTEXT");
//            props.put("sasl.mechanism", "PLAIN");
            props.put("sasl.mechanism", "SCRAM-SHA-256");
            props.put("bootstrap.servers", sourceDTO.getBrokerUrls());
//            props.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"admin\" password=\"Cdyanfa_123456\";");
            props.put(SaslConfigs.SASL_JAAS_CONFIG, String.format(KafkaConsistent.KAFKA_SASL_PLAIN_CONTENT, sourceDTO.getUsername(), sourceDTO.getPassword()));
            return props;
        }
        String brokerUrls = "";
        if (MapUtils.isEmpty(sourceDTO.getKerberosConfig())) {
            brokerUrls = getKafkaBroker(sourceDTO);
            /* 定义kakfa 服务的地址，不需要将所有broker指定上 */
            props.put("bootstrap.servers", brokerUrls);
            //不满足kerberos条件 直接返回
            return props;
        }
        log.error("lf++++++++++++++++++++++++kerberosConfig { "+sourceDTO.getKerberosConfig()+" }");
        // 只需要认证的用户名
        String kafkaKbrServiceName = MapUtils.getString(sourceDTO.getKerberosConfig(), HadoopConfTool.KAFKA_KERBEROS_SERVICE_NAME, DEFAULT_KERBEROS_NAME);
        if (StringUtils.isNotEmpty(kafkaKbrServiceName)) {
            kafkaKbrServiceName = kafkaKbrServiceName.split("/")[0];
            // kerberos 相关设置
            props.put("security.protocol", "SASL_PLAINTEXT");
            // kafka broker的启动配置
            props.put("sasl.kerberos.service.name", kafkaKbrServiceName);
        }

        String kafkaLoginConf = writeKafkaJaas(sourceDTO.getKerberosConfig());

        // 刷新kerberos认证信息，在设置完java.security.krb5.conf后进行，否则会使用上次的krb5文件进行 refresh 导致认证失败
        try {
            Config.refresh();
            javax.security.auth.login.Configuration.setConfiguration(null);
        } catch (Exception e) {
            log.error("Kafka kerberos authentication information refresh failed!");
        }
        props.put("sasl.mechanism.inter.broker.protocol", "GSSAPI");
        if(StringUtils.isNotEmpty(sourceDTO.getBrokerUrls())){
            brokerUrls=sourceDTO.getBrokerUrls();
        }else{
            brokerUrls = getKafkaBroker(sourceDTO);
        }

        log.info("Initialize Kafka configuration information, brokerUrls : {}, kerberosConfig : {}", brokerUrls, sourceDTO.getKerberosConfig());
        if (StringUtils.isBlank(brokerUrls)) {
            throw new DtLoaderException("Kafka Broker address cannot be empty");
        }
        /* 定义kakfa 服务的地址，不需要将所有broker指定上 */
        props.put("bootstrap.servers", brokerUrls);
        // kafka broker的启动配置
        System.setProperty("java.security.auth.login.config", kafkaLoginConf);
        System.setProperty("javax.security.auth.useSubjectCredsOnly", "false");

        log.error("lf+++++++++++++++++++++ { "+ props.toString()+"  }");
        return props;

    }


    public static List<String> getRecordsFromKafka(KafkaSourceDTO sourceDTO, String topic, String autoReset) {
        List<String> result = new ArrayList<>();
        Properties props = initProperties(sourceDTO);
        /*去除超时时间*/
        props.remove("request.timeout.ms");
        props.put("max.poll.records", MAX_POOL_RECORDS);

        // 获取kafka client
        /* 定义consumer */
        KafkaConsumer<String, String> consumer=null;
        try {
            consumer= new KafkaConsumer<>(props);
            List<TopicPartition> partitions = new ArrayList<>();
            List<PartitionInfo> all = consumer.partitionsFor(topic);
            for (PartitionInfo partitionInfo : all) {
                partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
            }

            consumer.assign(partitions);
            //如果消息没有被消费过，可能出现无法移动offset的情况导致报错
            //https://stackoverflow.com/questions/41008610/kafkaconsumer-0-10-java-api-error-message-no-current-assignment-for-partition
            //主动拉去一次消息
            consumer.poll(1000);
            //根据autoReset 设置位移
            if (EARLIEST.equals(autoReset)) {
                consumer.seekToBeginning(partitions);
            } else {
                Map<TopicPartition, Long> partitionLongMap = consumer.endOffsets(partitions);
                for (Map.Entry<TopicPartition, Long> entry : partitionLongMap.entrySet()) {
                    long offset = entry.getValue() - MAX_POOL_RECORDS;
                    offset = offset > 0 ? offset : 0;
                    consumer.seek(entry.getKey(), offset);
                }
            }

            /* 读取数据，读取超时时间为100ms */
            ConsumerRecords<String, String> records = consumer.poll(1000);
            for (ConsumerRecord<String, String> record : records) {
                String value = record.value();
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                if (result.size() >= MAX_POOL_RECORDS) {
                    break;
                }
                result.add(record.value());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new DtLoaderException(String.format("consumption data from kafka error: %s", e.getMessage()), e);
        } finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            destroyProperty();
        }
        return result;
    }

    public static List<JSONObject> getRecordsFromKafkaByStatistics(KafkaSourceDTO sourceDTO, Integer partition, String topic, String autoReset, String maxpoolrecords) {
        List<JSONObject> result = new ArrayList<>();
        Properties props = initProperties(sourceDTO);
        /*去除超时时间*/
        props.remove("request.timeout.ms");
        Integer maxpool = StringUtils.isEmpty(maxpoolrecords) ? 10 : Integer.valueOf(maxpoolrecords);
        props.put("max.poll.records", 10);

        // 获取kafka client
        /* 定义consumer */
        KafkaConsumer<String, String> consumer=null;
        try {
            consumer = new KafkaConsumer<>(props);
            List<TopicPartition> partitions = new ArrayList<>();
            List<PartitionInfo> all = consumer.partitionsFor(topic);
            for (PartitionInfo partitionInfo : all) {
                if (partitionInfo.partition() == partition) {
                    partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
                }

            }

            consumer.assign(partitions);
            //如果消息没有被消费过，可能出现无法移动offset的情况导致报错
            //https://stackoverflow.com/questions/41008610/kafkaconsumer-0-10-java-api-error-message-no-current-assignment-for-partition
            //主动拉去一次消息
            consumer.poll(1000);
            //根据autoReset 设置位移
            if (EARLIEST.equals(autoReset)) {
                consumer.seekToBeginning(partitions);
            } else {
                Map<TopicPartition, Long> partitionLongMap = consumer.endOffsets(partitions);
                for (Map.Entry<TopicPartition, Long> entry : partitionLongMap.entrySet()) {
                    long offset = entry.getValue() - maxpool;
                    offset = offset > 0 ? offset : 0;
                    consumer.seek(entry.getKey(), offset);
                }
            }

            /* 读取数据，读取超时时间为100ms */
            ConsumerRecords<String, String> records = consumer.poll(1000);
            for (ConsumerRecord<String, String> record : records) {
                String value = record.value();
//                if (StringUtils.isBlank(value)) {
//                    continue;
//                }
                if (result.size() >= maxpool) {
                    break;
                }
                JSONObject object = new JSONObject(new LinkedHashMap<>());
                object.put("partition", record.partition());
                object.put("offset", record.offset());
                object.put("msg", record.value());
                object.put("timespan", record.timestamp());
                object.put("date", DateUtils.convertUnixTime(record.timestamp()));
                result.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new DtLoaderException(String.format("consumption data from kafka error: %s", e.getMessage()), e);
        } finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            destroyProperty();
        }
        return result;
    }

    public static List<KafkaPartitionDTO> getPartitions(KafkaSourceDTO sourceDTO, String topic) {
        Properties defaultKafkaConfig = initProperties(sourceDTO);
        List<KafkaPartitionDTO> partitionDTOS = Lists.newArrayList();
        KafkaConsumer<String, String> consumer=null;
        try  {
            consumer= new KafkaConsumer<>(defaultKafkaConfig);
            // PartitionInfo没有实现序列化接口，不能使用 fastJson 进行拷贝
            List<PartitionInfo> partitions = consumer.partitionsFor(topic);
            if (CollectionUtils.isEmpty(partitions)) {
                return partitionDTOS;
            }
            for (PartitionInfo partition : partitions) {
                // 所有副本
                List<KafkaPartitionDTO.Node> replicas = Lists.newArrayList();
                for (Node node : partition.replicas()) {
                    replicas.add(buildKafkaPartitionNode(node));
                }
                // 在isr队列中的副本
                List<KafkaPartitionDTO.Node> inSyncReplicas = Lists.newArrayList();
                for (Node node : partition.inSyncReplicas()) {
                    inSyncReplicas.add(buildKafkaPartitionNode(node));
                }
                KafkaPartitionDTO kafkaPartitionDTO = KafkaPartitionDTO.builder()
                        .topic(partition.topic())
                        .partition(partition.partition())
                        .leader(buildKafkaPartitionNode(partition.leader()))
                        .replicas(replicas.toArray(new KafkaPartitionDTO.Node[]{}))
                        .inSyncReplicas(inSyncReplicas.toArray(new KafkaPartitionDTO.Node[]{}))
                        .build();
                partitionDTOS.add(kafkaPartitionDTO);
            }
            return partitionDTOS;
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get topic: %s partition information is exception：%s", topic, e.getMessage()), e);
        }finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
        }
    }

    /**
     * 构建kafka node
     *
     * @param node kafka副本信息
     * @return common-loader中定义的kafka副本信息
     */
    private static KafkaPartitionDTO.Node buildKafkaPartitionNode(Node node) {
        if (Objects.isNull(node)) {
            return KafkaPartitionDTO.Node.builder().build();
        }
        return KafkaPartitionDTO.Node.builder()
                .host(node.host())
                .id(node.id())
                .idString(node.idString())
                .port(node.port())
                .rack(node.rack())
                .build();
    }


    /**
     * 从 kafka 消费数据
     *
     * @param sourceDTO       kafka 数据源信息
     * @param topic           消费主题
     * @param collectNum      收集条数
     * @param offsetReset     消费方式
     * @param timestampOffset 按时间消费
     * @param maxTimeWait     最大等待时间
     * @return 消费到的数据
     */
    public static List<String> consumeData(KafkaSourceDTO sourceDTO, String topic, Integer collectNum,
                                           String offsetReset, Long timestampOffset, Integer maxTimeWait) {
        // 结果集
        List<String> result = new ArrayList<>();
        Properties prop = initProperties(sourceDTO);
        // 每次拉取最大条数
        prop.put("max.poll.records", MAX_POOL_RECORDS);
        KafkaConsumer<String, String> consumer=null;
        try  {
            consumer = new KafkaConsumer<>(prop);
            List<TopicPartition> partitions = Lists.newArrayList();
            // 获取所有的分区
            List<PartitionInfo> allPartitions = consumer.partitionsFor(topic);
            for (PartitionInfo partitionInfo : allPartitions) {
                partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
            }
            consumer.assign(partitions);

            // 从最早位置开始消费
            if (EConsumeType.EARLIEST.name().toLowerCase().equals(offsetReset)) {
                consumer.seekToBeginning(partitions);
            } else if (EConsumeType.TIMESTAMP.name().toLowerCase().equals(offsetReset) && Objects.nonNull(timestampOffset)) {
                Map<TopicPartition, Long> timestampsToSearch = Maps.newHashMap();
                for (TopicPartition partition : partitions) {
                    timestampsToSearch.put(partition, timestampOffset);
                }
                Map<TopicPartition, OffsetAndTimestamp> offsetsForTimes = consumer.offsetsForTimes(timestampsToSearch);
                // 没有找到offset 则从当前时间开始消费
                if (MapUtils.isEmpty(offsetsForTimes)) {
                    consumer.seekToEnd(partitions);
                } else {
                    for (Map.Entry<TopicPartition, OffsetAndTimestamp> entry : offsetsForTimes.entrySet()) {
                        consumer.seek(entry.getKey(), entry.getValue().offset());
                    }
                }
            } else {
                // 默认从最当前位置开始消费
                if (EConsumeType.LATEST.name().toLowerCase().equals(offsetReset)) {
                    consumer.seekToEnd(partitions);
                }
            }

            // 开始时间
            long start = System.currentTimeMillis();
            // 消费结束时间
            long endTime = start + maxTimeWait * 1000;
            while (true) {
                long nowTime = System.currentTimeMillis();
                if (nowTime >= endTime) {
                    break;
                }
                ConsumerRecords<String, String> records = consumer.poll(1000);
                for (ConsumerRecord<String, String> record : records) {
                    String value = record.value();
                    if (StringUtils.isBlank(value)) {
                        continue;
                    }
                    result.add(value);
                    if (result.size() >= collectNum) {
                        break;
                    }
                }
                if (result.size() >= collectNum) {
                    break;
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("consumption data from Kafka exception: %s", e.getMessage()), e);
        } finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            destroyProperty();
        }
        return result;
    }

    /**
     * 消费者详情列表
     *
     * @param sourceDTO
     * @return
     */
    public static List<ConsumerInfoDTO> listConsumerInfo(KafkaSourceDTO sourceDTO) {
        List<ConsumerInfoDTO> consumerGroups = new ArrayList<>();
        kafka.admin.AdminClient adminClient=null;
        try {

            List<String> consumerGroupsIds = new ArrayList<>();
            Properties prop = initProperties(sourceDTO);
            // 获取kafka client
            adminClient = kafka.admin.AdminClient.create(prop);
            // scala seq 转 java list
            List<GroupOverview> groups = JavaConversions.seqAsJavaList(adminClient.listAllGroupsFlattened().toSeq());
            groups.forEach(group -> consumerGroupsIds.add(group.groupId()));
            for (String consumerGroupsId : consumerGroupsIds) {
                AdminClient.ConsumerGroupSummary consumerGroupSummary = adminClient.describeConsumerGroup(consumerGroupsId, 5000);
                //获取consumergroup中的所有consumer
                ConsumerInfoDTO build = ConsumerInfoDTO.builder()
                        .id(consumerGroupsId)
                        .group(consumerGroupsId)
                        .node(consumerGroupSummary.coordinator().host().replaceAll("/", ""))

                        .build();
                consumerGroups.add(build);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if(Objects.nonNull(adminClient)){
                adminClient.close();
            }
        }
        return consumerGroups;
    }

    public static String getConsumerStatus(KafkaSourceDTO sourceDTO, String groupId) {
        kafka.admin.AdminClient adminClient = null;
        try {
            Properties prop = initProperties(sourceDTO);
            // 获取kafka client
            adminClient = kafka.admin.AdminClient.create(prop);
            Map<TopicPartition, Object> offsets = JavaConversions.mapAsJavaMap(adminClient.listGroupOffsets(groupId));
            kafka.admin.AdminClient.ConsumerGroupSummary consumerGroupSummary = adminClient.describeConsumerGroup(groupId, 5000);
            //获取consumergroup中的所有consumer
            List<kafka.admin.AdminClient.ConsumerSummary> consumerSummaries = JavaConversions.seqAsJavaList(consumerGroupSummary.consumers().get().toSeq());
            if (consumerSummaries != null && consumerSummaries.size() != 0) {
                return "Running";
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(Objects.nonNull(adminClient)){
                adminClient.close();
            }

        }
        return "Shutdown";
    }

    //组装active topic 树
    /*
     * <AUTHOR>
     * @Param  * @param sourceDTO
     * @param isConsumer 是否以consumer为维度，该树有两个维度一个从consumer出发，一个从active topic出发
     */
    public static JSONObject getAllActiveTopicsTree(KafkaSourceDTO sourceDTO, Boolean isConsumer) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("label", "Active Topics");
        kafka.admin.AdminClient adminClient = null;
        try {
            Properties prop = initProperties(sourceDTO);
            // 获取kafka client
            adminClient = kafka.admin.AdminClient.create(prop);
            List<GroupOverview> groups = JavaConversions.seqAsJavaList(adminClient.listAllGroupsFlattened().toSeq());
            List<JSONObject> childrenGroups = new ArrayList<>();
            if (isConsumer) {
                for (GroupOverview group : groups) {
                    JSONObject jsonObjectGroup = new JSONObject();
                    jsonObjectGroup.put("label", group.groupId());
                    childrenGroups.add(jsonObjectGroup);
                    kafka.admin.AdminClient.ConsumerGroupSummary consumerGroupSummary = adminClient.describeConsumerGroup(group.groupId(), 5000);
                    //获取consumergroup中的所有consumer
                    List<kafka.admin.AdminClient.ConsumerSummary> consumerSummaries = JavaConversions.seqAsJavaList(consumerGroupSummary.consumers().get().toSeq());
                    Set<JSONObject> childrenTopics = new HashSet<>();
                    consumerSummaries.forEach(item->{
                        List<TopicPartition> topicPartitions = JavaConversions.seqAsJavaList(item.assignment().toSeq());
                        topicPartitions.forEach(topic->{
                            JSONObject jsonObjectTopic = new JSONObject();
                            jsonObjectTopic.put("label", topic.topic());
                            childrenTopics.add(jsonObjectTopic);
                        });
                        jsonObjectGroup.put("children", childrenTopics);
                    });

                }
                jsonObject.put("children", childrenGroups);
            } else {

                List<JSONObject> topiclist = new ArrayList<>();
                Map<String, Set<JSONObject>> topicGroupHashMap = new HashMap<>();
                for (GroupOverview group : groups) {
                    kafka.admin.AdminClient.ConsumerGroupSummary consumerGroupSummary = adminClient.describeConsumerGroup(group.groupId(), 5000);
                    //获取consumergroup中的所有consumer
                    List<kafka.admin.AdminClient.ConsumerSummary> consumerSummaries = JavaConversions.seqAsJavaList(consumerGroupSummary.consumers().get().toSeq());
                    for (AdminClient.ConsumerSummary consumerSummary : consumerSummaries) {
                        List<TopicPartition> topicPartitions = JavaConversions.seqAsJavaList(consumerSummary.assignment().toSeq());
                        topicPartitions.forEach(topic->{
                            String topicName = topic.topic();
                            if (topicGroupHashMap.get(topicName) == null) {
                                Set<JSONObject> objects = new HashSet<>();
                                JSONObject jsonObject1 = new JSONObject();
                                jsonObject1.put("label",group.groupId());
                                objects.add(jsonObject1);
                                topicGroupHashMap.put(topicName, objects);
                            } else {
                                Set<JSONObject> strings = topicGroupHashMap.get(topic.topic());
                                JSONObject jsonObject1 = new JSONObject();
                                jsonObject1.put("label",group.groupId());
                                strings.add(jsonObject1);
                                topicGroupHashMap.put(topicName, strings);
                            }
                        });
                    }
                    consumerSummaries.forEach(item->{


                    });
                }
                for (String s : topicGroupHashMap.keySet()) {
                    JSONObject topicJson = new JSONObject();
                    topicJson.put("label", s);
                    topicJson.put("children", topicGroupHashMap.get(s));
                    topiclist.add(topicJson);
                }
                jsonObject.put("children", topiclist);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if(Objects.nonNull(adminClient)){
                adminClient.close();
            }
        }
        return jsonObject;
    }

    //根据groupId获取topic信息
    public static JSONObject listTopicInfoByGroupId(KafkaSourceDTO sourceDTO, String groupId) {
        JSONObject jsonObject = new JSONObject();
        Set<JSONObject> result = new HashSet<>();
        kafka.admin.AdminClient adminClient = null;
        try {
            Properties prop = initProperties(sourceDTO);
            // 获取kafka client
            adminClient = kafka.admin.AdminClient.create(prop);
            Map<TopicPartition, Object> offsets = JavaConversions.mapAsJavaMap(adminClient.listGroupOffsets(groupId));
            kafka.admin.AdminClient.ConsumerGroupSummary consumerGroupSummary = adminClient.describeConsumerGroup(groupId, 5000);
            //获取consumergroup中的所有consumer
            List<kafka.admin.AdminClient.ConsumerSummary> consumerSummaries = JavaConversions.seqAsJavaList(consumerGroupSummary.consumers().get().toSeq());
            Set<String> activeTopics = new HashSet<>();
            int activerCounter = 0;
            for (AdminClient.ConsumerSummary consumerSummary : consumerSummaries) {
                List<TopicPartition> topicPartitions = JavaConversions.seqAsJavaList(consumerSummary.assignment().toSeq());
                for (TopicPartition topicPartition : topicPartitions) {
                    activeTopics.add(topicPartition.topic());
                }

                if (StringUtils.isNotEmpty(consumerSummary.consumerId())) {
                    activerCounter++;
                }
            }

            try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(prop)) {
                for (TopicPartition topicPartition : offsets.keySet()) {
                    {
                        // 当前消费位置
                        List<TopicPartition> singleTopicPartition = Lists.newArrayList(topicPartition);
                        // 指定当前分区
                        consumer.assign(singleTopicPartition);
                        consumer.seekToEnd(singleTopicPartition);
                        long logEndOffset = consumer.position(topicPartition);
                        consumer.assign(Collections.singleton(topicPartition));
                        java.util.Map<TopicPartition, Long> endLogSize = consumer.endOffsets(Collections.singleton(topicPartition));
                        java.util.Map<TopicPartition, Long> startLogSize = consumer.beginningOffsets(Collections.singleton(topicPartition));
                        long realLogSize = endLogSize.get(topicPartition).longValue() - (long) offsets.get(topicPartition);
                        String status = "";
                        if (logEndOffset == 0) {
                            /**
                             * logsize equal offsets,follow two states.<br>
                             * 1. maybe application shutdown.<br>
                             * 2. maybe application run, but producer rate equal
                             * consumer rate.<br>
                             */
                            if (realLogSize == 0) {
                                status = "PENDING";
                            } else {
                                status = "SHUTDOWN";
                            }
                        } else {
                            status = "RUNNING";
                        }
                        if (activeTopics.size() == 0) {
                            status = "SHUTDOWN";
                        }
                        JSONObject object = new JSONObject();
                        object.put("topic", topicPartition.topic());
//                        object.put("partition", topicPartition.partition());
                        object.put("status", status);

                        result.add(object);
                    }
                }
            }

            jsonObject.put("activetops", activeTopics.size());
            jsonObject.put("topicSubs", result);
            jsonObject.put("activeThreads", activerCounter);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(Objects.nonNull(adminClient)){
                adminClient.close();
            }
        }
        return jsonObject;
    }

    /**
     * 获取 kafka 消费者组列表
     *
     * @param sourceDTO kakfa 数据源信息
     * @param topic     kafka 主题
     * @return 消费者组列表
     */
    public static List<String> listConsumerGroup(KafkaSourceDTO sourceDTO, String topic) {
        List<String> consumerGroups = new ArrayList<>();
        Properties prop = initProperties(sourceDTO);
        // 获取kafka client
        AdminClient adminClient = AdminClient.create(prop);
        try {
            // scala seq 转 java list
            List<GroupOverview> groups = JavaConversions.seqAsJavaList(adminClient.listAllGroupsFlattened().toSeq());
            groups.forEach(group ->
                    consumerGroups.add(group.groupId())

            );
            // 不指定topic 全部返回
            if (StringUtils.isBlank(topic)) {
                return consumerGroups;
            }
            List<String> consumerGroupsByTopic = Lists.newArrayList();
            for (String groupId : consumerGroups) {
                kafka.admin.AdminClient.ConsumerGroupSummary groupSummary = adminClient.describeConsumerGroup(groupId, 5000L);
                // 消费者组不存在的情况
                if (Objects.isNull(groupSummary) || "Dead".equals(groupSummary.state()) || groupId.contains("efak")) {
                    continue;
                }
                Map<TopicPartition, Object> offsets = JavaConversions.mapAsJavaMap(adminClient.listGroupOffsets(groupId));
                for (TopicPartition topicPartition : offsets.keySet()) {
                    if (topic.equals(topicPartition.topic())) {
                        consumerGroupsByTopic.add(groupId);
                        break;
                    }
                }
            }
            return consumerGroupsByTopic;
        } catch (Exception e) {
            log.error("listConsumerGroup error:{}", e.getMessage(), e);
        } finally {
            if (Objects.nonNull(adminClient)) {
                adminClient.close();
            }
            destroyProperty();
        }
        return Lists.newArrayList();
    }

    //获取broker的cpu
    public static String getBrokerCpuUse(String host, Integer port) {
        String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, host, port);
        JMXConnector connector = null;
        try {
            //连接节点的jmx 获取监控信息
            connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
            MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
            String value = mbeanConnection.getAttribute(new ObjectName(KafkaIndicatorName.JMX_PERFORMANCE_TYPE.getValue()), KafkaIndicatorName.PROCESS_CPU_LOAD.getValue()).toString();
            double cpuValue = Double.parseDouble(value);
            String percent = StrUtils.numberic((cpuValue * 100.0) + "") + "";
            return percent;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JMX service url[" + host + "] create has error,msg is ", e);
            return "";
        } finally {
            try {
                connector.close();
            } catch (Exception e) {
                log.error("Close JMXConnector[" + host + "] has error,msg is ", e);
            }
        }

    }

    //获取broker的cpu
    public static double getUsedCpuValue(String host, Integer port) {
        String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, host, port);
        JMXConnector connector = null;
        try {
            //连接节点的jmx 获取监控信息
            connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
            MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
            String value = mbeanConnection.getAttribute(new ObjectName(KafkaIndicatorName.JMX_PERFORMANCE_TYPE.getValue()), KafkaIndicatorName.PROCESS_CPU_LOAD.getValue()).toString();
            double cpuValue = Double.parseDouble(value);
            return cpuValue;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JMX service url[" + host + "] create has error,msg is ", e);
            return 0.0;
        } finally {
            try {
                connector.close();
            } catch (Exception e) {
                log.error("Close JMXConnector[" + host + "] has error,msg is ", e);
            }
        }

    }

    //获取broker的cpugetTopIcMetaByTopIc
    public static String getBrokerKafkaVersion(String host, Integer port, String id) {
        String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, host, port);
        JMXConnector connector = null;
        try {
            //连接节点的jmx 获取监控信息
            connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
            MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
            String value = mbeanConnection.getAttribute(new ObjectName(String.format(KafkaIndicatorName.BROKER_VERSION.getValue(), id)), KafkaIndicatorName.BROKER_VERSION_VALUE.getValue()).toString();
            return value;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JMX service url[" + host + "] create has error,msg is ", e);
            return "";
        } finally {
            try {
                connector.close();
            } catch (Exception e) {
                log.error("Close JMXConnector[" + host + "] has error,msg is ", e);
            }
        }

    }

    //获取broker的存储使用率
    public static String getBrokerMemoryUse(String host, Integer port) {
        String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, host, port);
        JMXConnector connector = null;
        try {
            //连接节点的jmx 获取监控信息
            connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
            MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
            MemoryMXBean memBean = ManagementFactory.newPlatformMXBeanProxy(mbeanConnection, ManagementFactory.MEMORY_MXBEAN_NAME, MemoryMXBean.class);
            long used = memBean.getHeapMemoryUsage().getUsed();
            String percent = StrUtils.stringify(used);
            return percent;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JMX service url[" + host + "] create has error,msg is ", e);
            return "";
        } finally {
            try {
                connector.close();
            } catch (Exception e) {
                log.error("Close JMXConnector[" + host + "] has error,msg is ", e);
            }
        }

    }

    //获取broker的存储使用率
    public static String getBrokerMemoryUsePercent(String host, Integer port) {
        String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, host, port);
        JMXConnector connector = null;
        try {
            //连接节点的jmx 获取监控信息
            connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
            MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
            MemoryMXBean memBean = ManagementFactory.newPlatformMXBeanProxy(mbeanConnection, ManagementFactory.MEMORY_MXBEAN_NAME, MemoryMXBean.class);
            long used = memBean.getHeapMemoryUsage().getUsed();
            long max = memBean.getHeapMemoryUsage().getMax();
            String percent = StrUtils.numberic((used * 100.0 / max) + "") + "";
            return percent;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("JMX service url[" + host + "] create has error,msg is ", e);
            return "";
        } finally {
            try {
                connector.close();
            } catch (Exception e) {
                log.error("Close JMXConnector[" + host + "] has error,msg is ", e);
            }
        }


    }

    public static Map<String, KafkaMonitorDTO> getTopicMonitor(String zkUrls, String topic) {
        log.info("Obtain Kafka Broker address through ZK : {}", zkUrls);
        if (StringUtils.isBlank(zkUrls) || !TelUtil.checkTelnetAddr(zkUrls)) {
            throw new DtLoaderException("Please configure the correct zookeeper address");
        }
        Map<String, KafkaMonitorDTO> mbeans = new HashMap<>();
        try {
            List<BrokersDTO> brokerInfoList = getBrokerInfoList(zkUrls);

            String topicbytesIn = KafkaIndicatorName.BYTES_IN_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
            String topicbytesOut = KafkaIndicatorName.BYTES_OUT_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
            String topicbytesRejected = KafkaIndicatorName.BYTES_REJECTED_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
            String topicfailedFetchRequest = KafkaIndicatorName.FAILED_FETCH_REQUESTS_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
            String topicfailedProduceRequest = KafkaIndicatorName.FAILED_PRODUCE_REQUESTS_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
            String topicmessageIn = KafkaIndicatorName.MESSAGES_IN_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
            String topicproduceMessageConversions = KafkaIndicatorName.PRODUCE_MESSAGE_CONVERSIONS_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
            String topictotalFetchRequests = KafkaIndicatorName.TOTAL_FETCH_REQUESTS_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
            String topictotalProduceRequests = KafkaIndicatorName.TOTAL_PRODUCE_REQUESTS_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
            for (BrokersDTO broker : brokerInfoList) {
                KafkaMonitorDTO bytesIn = common(broker.getHost(), broker.getJmxPort(), topicbytesIn);
                KafkaMonitorDTO bytesOut = common(broker.getHost(), broker.getJmxPort(), topicbytesOut);
                KafkaMonitorDTO bytesRejected = common(broker.getHost(), broker.getJmxPort(), topicbytesRejected);
                KafkaMonitorDTO failedFetchRequest = common(broker.getHost(), broker.getJmxPort(), topicfailedFetchRequest);
                KafkaMonitorDTO failedProduceRequest = common(broker.getHost(), broker.getJmxPort(), topicfailedProduceRequest);
                KafkaMonitorDTO messageIn = common(broker.getHost(), broker.getJmxPort(), topicmessageIn);
                KafkaMonitorDTO produceMessageConversions = common(broker.getHost(), broker.getJmxPort(), topicproduceMessageConversions);
                KafkaMonitorDTO totalFetchRequests = common(broker.getHost(), broker.getJmxPort(), topictotalFetchRequests);
                KafkaMonitorDTO totalProduceRequests = common(broker.getHost(), broker.getJmxPort(), topictotalProduceRequests);
                assembleMBeanInfo(mbeans, KafkaIndicatorName.MESSAGES_IN.getValue(), messageIn);
                assembleMBeanInfo(mbeans, KafkaIndicatorName.BYTES_IN.getValue(), bytesIn);
                assembleMBeanInfo(mbeans, KafkaIndicatorName.BYTES_OUT.getValue(), bytesOut);
                assembleMBeanInfo(mbeans, KafkaIndicatorName.BYTES_REJECTED.getValue(), bytesRejected);
                assembleMBeanInfo(mbeans, KafkaIndicatorName.FAILED_FETCH_REQUEST.getValue(), failedFetchRequest);
                assembleMBeanInfo(mbeans, KafkaIndicatorName.FAILED_PRODUCE_REQUEST.getValue(), failedProduceRequest);
                assembleMBeanInfo(mbeans, KafkaIndicatorName.PRODUCEMESSAGECONVERSIONS.getValue(), produceMessageConversions);
                assembleMBeanInfo(mbeans, KafkaIndicatorName.TOTALFETCHREQUESTSPERSEC.getValue(), totalFetchRequests);
                assembleMBeanInfo(mbeans, KafkaIndicatorName.TOTALPRODUCEREQUESTSPERSEC.getValue(), totalProduceRequests);
            }

            for (Map.Entry<String, KafkaMonitorDTO> entry : mbeans.entrySet()) {
                if (entry == null || entry.getValue() == null) {
                    continue;
                }
                entry.getValue().setFifteenMinute(StrUtils.assembly(entry.getValue().getFifteenMinute()));
                entry.getValue().setFiveMinute(StrUtils.assembly(entry.getValue().getFiveMinute()));
                entry.getValue().setMeanRate(StrUtils.assembly(entry.getValue().getMeanRate()));
                entry.getValue().setOneMinute(StrUtils.assembly(entry.getValue().getOneMinute()));
            }


        } catch (Exception e) {
            e.printStackTrace();
        }

        return mbeans;


    }

    public static Map<String, KafkaMonitorDTO> getOnlineAllBrokersMBean(String host, Integer jmxPort) {

        Map<String, KafkaMonitorDTO> mbeans = new HashMap<>();
        try {

            String topicbytesIn = KafkaIndicatorName.BYTES_IN_PER_SEC.getValue();
            String topicbytesOut = KafkaIndicatorName.BYTES_OUT_PER_SEC.getValue();
            String topicbytesRejected = KafkaIndicatorName.BYTES_REJECTED_PER_SEC.getValue();
            String topicfailedFetchRequest = KafkaIndicatorName.FAILED_FETCH_REQUESTS_PER_SEC.getValue();
            String topicfailedProduceRequest = KafkaIndicatorName.FAILED_PRODUCE_REQUESTS_PER_SEC.getValue();
            String topicmessageIn = KafkaIndicatorName.MESSAGES_IN_PER_SEC.getValue();
            String topicproduceMessageConversions = KafkaIndicatorName.PRODUCE_MESSAGE_CONVERSIONS_PER_SEC.getValue();
            String topictotalFetchRequests = KafkaIndicatorName.TOTAL_FETCH_REQUESTS_PER_SEC.getValue();
            String topictotalProduceRequests = KafkaIndicatorName.TOTAL_PRODUCE_REQUESTS_PER_SEC.getValue();
            KafkaMonitorDTO bytesIn = common(host, jmxPort, topicbytesIn);
            KafkaMonitorDTO bytesOut = common(host, jmxPort, topicbytesOut);
            KafkaMonitorDTO bytesRejected = common(host, jmxPort, topicbytesRejected);
            KafkaMonitorDTO failedFetchRequest = common(host, jmxPort, topicfailedFetchRequest);
            KafkaMonitorDTO failedProduceRequest = common(host, jmxPort, topicfailedProduceRequest);
            KafkaMonitorDTO messageIn = common(host, jmxPort, topicmessageIn);
            KafkaMonitorDTO produceMessageConversions = common(host, jmxPort, topicproduceMessageConversions);
            KafkaMonitorDTO totalFetchRequests = common(host, jmxPort, topictotalFetchRequests);
            KafkaMonitorDTO totalProduceRequests = common(host, jmxPort, topictotalProduceRequests);
            assembleMBeanInfo(mbeans, KafkaIndicatorName.MESSAGES_IN.getValue(), messageIn);
            assembleMBeanInfo(mbeans, KafkaIndicatorName.BYTES_IN.getValue(), bytesIn);
            assembleMBeanInfo(mbeans, KafkaIndicatorName.BYTES_OUT.getValue(), bytesOut);
            assembleMBeanInfo(mbeans, KafkaIndicatorName.BYTES_REJECTED.getValue(), bytesRejected);
            assembleMBeanInfo(mbeans, KafkaIndicatorName.FAILED_FETCH_REQUEST.getValue(), failedFetchRequest);
            assembleMBeanInfo(mbeans, KafkaIndicatorName.FAILED_PRODUCE_REQUEST.getValue(), failedProduceRequest);
            assembleMBeanInfo(mbeans, KafkaIndicatorName.PRODUCEMESSAGECONVERSIONS.getValue(), produceMessageConversions);
            assembleMBeanInfo(mbeans, KafkaIndicatorName.TOTALFETCHREQUESTSPERSEC.getValue(), totalFetchRequests);
            assembleMBeanInfo(mbeans, KafkaIndicatorName.TOTALPRODUCEREQUESTSPERSEC.getValue(), totalProduceRequests);

            for (Map.Entry<String, KafkaMonitorDTO> entry : mbeans.entrySet()) {
                if (entry == null || entry.getValue() == null) {
                    continue;
                }
                entry.getValue().setFifteenMinute(StrUtils.assembly(entry.getValue().getFifteenMinute()));
                entry.getValue().setFiveMinute(StrUtils.assembly(entry.getValue().getFiveMinute()));
                entry.getValue().setMeanRate(StrUtils.assembly(entry.getValue().getMeanRate()));
                entry.getValue().setOneMinute(StrUtils.assembly(entry.getValue().getOneMinute()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return mbeans;

    }

    private static void assembleMBeanInfo(Map<String, KafkaMonitorDTO> mbeans, String mBeanInfoKey, KafkaMonitorDTO mBeanInfo) {
        if (mbeans.containsKey(mBeanInfoKey) && mBeanInfo != null) {
            KafkaMonitorDTO mbeanInfo = mbeans.get(mBeanInfoKey);
            String fifteenMinuteOld = mbeanInfo.getFifteenMinute() == null ? "0.0" : mbeanInfo.getFifteenMinute();
            String fifteenMinuteLastest = mBeanInfo.getFifteenMinute() == null ? "0.0" : mBeanInfo.getFifteenMinute();
            String fiveMinuteOld = mbeanInfo.getFiveMinute() == null ? "0.0" : mbeanInfo.getFiveMinute();
            String fiveMinuteLastest = mBeanInfo.getFiveMinute() == null ? "0.0" : mBeanInfo.getFiveMinute();
            String meanRateOld = mbeanInfo.getMeanRate() == null ? "0.0" : mbeanInfo.getMeanRate();
            String meanRateLastest = mBeanInfo.getMeanRate() == null ? "0.0" : mBeanInfo.getMeanRate();
            String oneMinuteOld = mbeanInfo.getOneMinute() == null ? "0.0" : mbeanInfo.getOneMinute();
            String oneMinuteLastest = mBeanInfo.getOneMinute() == null ? "0.0" : mBeanInfo.getOneMinute();
            long fifteenMinute = Math.round(StrUtils.numberic(fifteenMinuteOld)) + Math.round(StrUtils.numberic(fifteenMinuteLastest));
            long fiveMinute = Math.round(StrUtils.numberic(fiveMinuteOld)) + Math.round(StrUtils.numberic(fiveMinuteLastest));
            long meanRate = Math.round(StrUtils.numberic(meanRateOld)) + Math.round(StrUtils.numberic(meanRateLastest));
            long oneMinute = Math.round(StrUtils.numberic(oneMinuteOld)) + Math.round(StrUtils.numberic(oneMinuteLastest));
            mbeanInfo.setFifteenMinute(String.valueOf(fifteenMinute));
            mbeanInfo.setFiveMinute(String.valueOf(fiveMinute));
            mbeanInfo.setMeanRate(String.valueOf(meanRate));
            mbeanInfo.setOneMinute(String.valueOf(oneMinute));
            mbeanInfo.setRate(mBeanInfoKey);
        } else {
            mbeans.put(mBeanInfoKey, mBeanInfo);
        }
    }


    //获取topic broker 监控指标
    public static KafkaMonitorDTO common(String host, Integer port, String monitorName) {
        JMXConnector connector = null;
        String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, host, port);
        KafkaMonitorDTO kafkaMonitorDTO = new KafkaMonitorDTO();
        kafkaMonitorDTO.setRate(monitorName);
        try {
            //连接节点的jmx 获取监控信息
            connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
            MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
            if (mbeanConnection.isRegistered(new ObjectName(monitorName))) {
                Object fifteenMinuteRate = mbeanConnection.getAttribute(new ObjectName(monitorName), KafkaIndicatorName.FIFTEEN_MINUTE_RATE.getValue());
                Object fiveMinuteRate = mbeanConnection.getAttribute(new ObjectName(monitorName), KafkaIndicatorName.FIVE_MINUTE_RATE.getValue());
                Object meanRate = mbeanConnection.getAttribute(new ObjectName(monitorName), KafkaIndicatorName.MEAN_RATE.getValue());
                Object oneMinuteRate = mbeanConnection.getAttribute(new ObjectName(monitorName), KafkaIndicatorName.ONE_MINUTE_RATE.getValue());
                kafkaMonitorDTO.setFifteenMinute(fifteenMinuteRate.toString());
                kafkaMonitorDTO.setFiveMinute(fiveMinuteRate.toString());
                kafkaMonitorDTO.setMeanRate(meanRate.toString());
                kafkaMonitorDTO.setOneMinute(oneMinuteRate.toString());

            } else {
                kafkaMonitorDTO.setFifteenMinute("0.0");
                kafkaMonitorDTO.setFiveMinute("0.0");
                kafkaMonitorDTO.setMeanRate("0.0");
                kafkaMonitorDTO.setOneMinute("0.0");
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("JMX service url[" + host + "] create has error,msg is ", e);
            kafkaMonitorDTO.setFifteenMinute("0.0");
            kafkaMonitorDTO.setFiveMinute("0.0");
            kafkaMonitorDTO.setMeanRate("0.0");
            kafkaMonitorDTO.setOneMinute("0.0");
        } finally {
            try {
                connector.close();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("Close JMXConnector[" + host + "] has error,msg is ", e);
            }
        }
        return kafkaMonitorDTO;


    }



    /**
     * 获取topic的详细信息
     *
     * @param sourceDTO
     * @param topIcName
     * @return
     */
    public static List<TopIcMetaInfoDTO> getTopIcMetaByTopIc(KafkaSourceDTO sourceDTO, String topIcName) {
        List<TopIcMetaInfoDTO> result = new ArrayList<>();
        Properties prop = initProperties(sourceDTO);
        // 获取kafka consumer
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(prop);
        List<PartitionInfo> partitionInfos = consumer.partitionsFor(topIcName);
        ZkUtils  zkUtils = ZkUtils.apply(sourceDTO.getUrl(), KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        ZkClient zkClient = zkUtils.zkClient();
        try {
            for (PartitionInfo partitionInfo : partitionInfos) {
                TopicPartition tp = new TopicPartition(topIcName, partitionInfo.partition());
                consumer.assign(Collections.singleton(tp));
                java.util.Map<TopicPartition, Long> endLogSize = consumer.endOffsets(Collections.singleton(tp));
                java.util.Map<TopicPartition, Long> startLogSize = consumer.beginningOffsets(Collections.singleton(tp));
                long realLogSize = endLogSize.get(tp).longValue() - startLogSize.get(tp).longValue();
                Node[] replicas = partitionInfo.replicas();
                List<Integer> replicaslist = new ArrayList<>();
                for (Node replica : replicas) {
                    replicaslist.add(replica.id());
                }
                Node[] nodes = partitionInfo.inSyncReplicas();
                List<Integer> nodeslist = new ArrayList<>();
                for (Node node : nodes) {
                    nodeslist.add(node.id());
                }
                //获取topic的创建时间和修改时间
                Stat stat = new Stat();

                String test = zkUtils.getTopicPartitionPath(topIcName, partitionInfo.partition());
                Object o = zkClient.readData(test, stat);

                TopIcMetaInfoDTO build = TopIcMetaInfoDTO.builder()
                        .topic(topIcName)
                        .partitionId(partitionInfo.partition())
                        .replicas(replicaslist.toString())
                        .leader(partitionInfo.leader().id())
                        .logSize(realLogSize)
                        .isr(nodeslist.toString())
                        .createTime(DateUtil.getFormattedDate(stat.getCtime(), "yyyy-MM-dd HH:mm"))
                        .updateTime(DateUtil.getFormattedDate(stat.getMtime(), "yyyy-MM-dd HH:mm"))
                        .build();
                List<Integer> isrIntegers = new ArrayList<>();
                List<Integer> replicasIntegers = new ArrayList<>();
                try {
                    String isr = build.getIsr();
                    isrIntegers = JSON.parseObject(build.getIsr(), new TypeReference<ArrayList<Integer>>() {
                    });
                    replicasIntegers = JSON.parseObject(build.getReplicas(), new TypeReference<ArrayList<Integer>>() {
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (isrIntegers.size() != replicasIntegers.size()) {
                    // replicas lost
                    build.setUnderReplicated(true);
                } else {
                    // replicas normal
                    build.setUnderReplicated(false);
                }
                if (replicasIntegers != null && replicasIntegers.size() > 0 && replicasIntegers.get(0) == build.getLeader()) {
                    // partition preferred leader
                    build.setPreferredLeader(true);
                } else {
                    // partition occurs preferred leader exception
                    build.setPreferredLeader(false);
                }
                result.add(build);


            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            if(Objects.nonNull(zkUtils)){
                zkUtils.close();
            }
            if(Objects.nonNull(zkClient)){
                zkClient.close();
            }
        }



        return result;
    }


    /**
     * Get kafka os memory.
     */
    public static double getOSMemory(String zkurls) {
        ZkUtils zkUtils = ZkUtils.apply(zkurls, KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        List<BrokersDTO> brokersByZk = getBrokersByZk(zkUtils);
        JMXConnector connector = null;
//        long total = 0L;
        long useds = 0L;
        long maxs = 0L;
        try {
            for (BrokersDTO brokersDTO : brokersByZk) {
                String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, brokersDTO.getHost(), brokersDTO.getJmxPort());
                //连接节点的jmx 获取监控信息

                connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
                MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
                MemoryMXBean memBean = ManagementFactory.newPlatformMXBeanProxy(mbeanConnection, ManagementFactory.MEMORY_MXBEAN_NAME, MemoryMXBean.class);
                long max = memBean.getHeapMemoryUsage().getMax();
                long used = memBean.getHeapMemoryUsage().getUsed();
//                total = total + max;
                useds = useds + used;
                maxs = maxs + max;
                if (connector != null) {
                    try {
                        connector.close();
                    } catch (IOException e) {
                        log.error("Close kafka used cpu value jmx connector has error, msg is " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull( connector)) {
                try {
                    connector.close();
                } catch (IOException e) {
                    log.error("Close kafka used cpu value jmx connector has error, msg is " + e.getMessage());
                }
            }
            if (Objects.nonNull( zkUtils)) {
                zkUtils.close();
            }
        }
        BigDecimal divide = new BigDecimal(0L);
        if (maxs != 0L) {
            BigDecimal bigDecimaluseds = BigDecimal.valueOf(useds);
            BigDecimal bigDecimalmaxs = BigDecimal.valueOf(maxs);
            BigDecimal a = bigDecimaluseds.divide(bigDecimalmaxs, 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal size = BigDecimal.valueOf(brokersByZk.size());
            divide = a.divide(size, 4, BigDecimal.ROUND_HALF_UP);
        }

        return divide.doubleValue();


    }

    public static long getOSMemory(String zkurls, String monitorName) {
        ZkUtils zkUtils = ZkUtils.apply(zkurls, KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        List<BrokersDTO> brokersByZk = getBrokersByZk(zkUtils);
        JMXConnector connector = null;
        long memory = 0L;
        try {
            for (BrokersDTO brokersDTO : brokersByZk) {
                String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, brokersDTO.getHost(), brokersDTO.getJmxPort());
                //连接节点的jmx 获取监控信息

                connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
                MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
                MemoryMXBean memBean = ManagementFactory.newPlatformMXBeanProxy(mbeanConnection, ManagementFactory.MEMORY_MXBEAN_NAME, MemoryMXBean.class);
                long max = memBean.getHeapMemoryUsage().getMax();
                long used = memBean.getHeapMemoryUsage().getUsed();
                if (KafkaIndicatorName.TOTAL_PHYSICAL_MEMORY_SIZE.getValue().equals(monitorName)) {
                    memory = max;
                } else if (KafkaIndicatorName.FREE_PHYSICAL_MEMORY_SIZE.getValue().equals(monitorName)) {
                    memory = max - used;
                }
                if (connector != null) {
                    try {
                        connector.close();
                    } catch (IOException e) {
                        log.error("Close kafka used cpu value jmx connector has error, msg is " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull(connector )) {
                try {
                    connector.close();
                } catch (IOException e) {
                    log.error("Close kafka used cpu value jmx connector has error, msg is " + e.getMessage());
                }
            }
            if (Objects.nonNull(zkUtils )) {
                zkUtils.close();
            }
        }
        return memory;
    }

    /**
     * Get kafka os memory.
     */
    public static double getCpuUsed(String zkurls) {
        ZkUtils zkUtils = ZkUtils.apply(zkurls, KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        List<BrokersDTO> brokersByZk = getBrokersByZk(zkUtils);
        JMXConnector connector = null;
        double cpu = 0.0000000000;
        Boolean isOpenJMX = false;
        try {
            for (BrokersDTO brokersDTO : brokersByZk) {
                if (-1 != brokersDTO.getJmxPort()) {
                    isOpenJMX = true;
                }
                if (isOpenJMX) {
                    String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, brokersDTO.getHost(), brokersDTO.getJmxPort());
                    //连接节点的jmx 获取监控信息
                    connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
                    MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
                    String value = mbeanConnection.getAttribute(new ObjectName(KafkaIndicatorName.JMX_PERFORMANCE_TYPE.getValue()), KafkaIndicatorName.PROCESS_CPU_LOAD.getValue()).toString();
                    double cpuValue = Double.parseDouble(value);
                    cpu = cpu + cpuValue;
                    if (connector != null) {
                        try {
                            connector.close();
                        } catch (IOException e) {
                            log.error("Close kafka used cpu value jmx connector has error, msg is " + e.getMessage());
                        }
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull(connector)) {
                try {
                    connector.close();
                } catch (IOException e) {
                    log.error("Close kafka used cpu value jmx connector has error, msg is " + e.getMessage());
                }
            }
            if(Objects.nonNull(zkUtils)){
                zkUtils.close();
            }
        }
        BigDecimal bigDecimalcpu = BigDecimal.valueOf(cpu);
        BigDecimal size = BigDecimal.valueOf(brokersByZk.size());
        BigDecimal divide = bigDecimalcpu.divide(size, 4, BigDecimal.ROUND_HALF_UP);

        return divide.doubleValue();


    }

    private static final String[] BROKER_KPIS = new String[]{KConstants.MESSAGEIN, KConstants.BYTEIN,
            KConstants.BYTEOUT, KConstants.BYTESREJECTED, KConstants.FAILEDFETCHREQUEST,
            KConstants.FAILEDPRODUCEREQUEST, KConstants.PRODUCEMESSAGECONVERSIONS,
            KConstants.TOTALFETCHREQUESTSPERSEC, KConstants.TOTALPRODUCEREQUESTSPERSEC, KConstants.REPLICATIONBYTESINPERSEC, KConstants.REPLICATIONBYTESOUTPERSEC,
            KConstants.OSTOTALMEMORY, KConstants.OSFREEMEMORY, KConstants.CPUUSED};

    public static List<KpiInfoDTO> getBrokerKpiInfos(String zkUrl) {
        List<KpiInfoDTO> list = new ArrayList<>();
        List<BrokersDTO> brokerInfoList = getBrokerInfoList(zkUrl);
        for (String kpi : BROKER_KPIS) {
            KpiInfoDTO kpiInfo = new KpiInfoDTO();
            kpiInfo.setTm(DateUtils.getCustomDate("yyyyMMdd"));
            kpiInfo.setTimespan(DateUtils.getTimeSpan());
            kpiInfo.setKey(kpi);
            for (BrokersDTO brokersDTO : brokerInfoList) {
                kafkaAssembly(brokersDTO.getHost(), brokersDTO.getJmxPort(), kpi, kpiInfo, zkUrl);
            }
            list.add(kpiInfo);
        }


        return list;
    }

    private static void kafkaAssembly(String host, Integer port, String type, KpiInfoDTO kpiInfoDTO, String zkUrl) {
        switch (type) {
            case KConstants.MESSAGEIN:

                KafkaMonitorDTO msg = common(host, port, KafkaIndicatorName.MESSAGES_IN_PER_SEC.getValue());
                ;
                if (msg != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(msg.getOneMinute()) + "");
                }
                break;
            case KConstants.BYTEIN:
                KafkaMonitorDTO bin = common(host, port, KafkaIndicatorName.BYTES_IN_PER_SEC.getValue());
                if (bin != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(bin.getOneMinute()) + "");
                }
                break;
            case KConstants.BYTEOUT:
                KafkaMonitorDTO bout = common(host, port, KafkaIndicatorName.BYTES_OUT_PER_SEC.getValue());
                if (bout != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(bout.getOneMinute()) + "");
                }
                break;
            case KConstants.BYTESREJECTED:
                KafkaMonitorDTO bytesRejectedPerSec = common(host, port, KafkaIndicatorName.BYTES_REJECTED_PER_SEC.getValue());
                if (bytesRejectedPerSec != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(bytesRejectedPerSec.getOneMinute()) + "");
                }
                break;
            case KConstants.FAILEDFETCHREQUEST:
                KafkaMonitorDTO failedFetchRequestsPerSec = common(host, port, KafkaIndicatorName.FAILED_FETCH_REQUESTS_PER_SEC.getValue());
                if (failedFetchRequestsPerSec != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(failedFetchRequestsPerSec.getOneMinute()) + "");
                }
                break;
            case KConstants.FAILEDPRODUCEREQUEST:
                KafkaMonitorDTO failedProduceRequestsPerSec = common(host, port, KafkaIndicatorName.FAILED_PRODUCE_REQUESTS_PER_SEC.getValue());
                if (failedProduceRequestsPerSec != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(failedProduceRequestsPerSec.getOneMinute()) + "");
                }
                break;
            case KConstants.PRODUCEMESSAGECONVERSIONS:
                KafkaMonitorDTO totalFetchRequests = common(host, port, KafkaIndicatorName.PRODUCE_MESSAGE_CONVERSIONS_PER_SEC.getValue());
                if (totalFetchRequests != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(totalFetchRequests.getOneMinute()) + "");
                }
                break;
            case KConstants.TOTALFETCHREQUESTSPERSEC:
                KafkaMonitorDTO totalProduceRequestsPerSec = common(host, port, KafkaIndicatorName.TOTAL_FETCH_REQUESTS_PER_SEC.getValue());
                if (totalProduceRequestsPerSec != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(totalProduceRequestsPerSec.getOneMinute()) + "");
                }
                break;
            case KConstants.TOTALPRODUCEREQUESTSPERSEC:
                KafkaMonitorDTO replicationBytesInPerSec = common(host, port, KafkaIndicatorName.TOTAL_PRODUCE_REQUESTS_PER_SEC.getValue());
                if (replicationBytesInPerSec != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(replicationBytesInPerSec.getOneMinute()) + "");
                }
                break;
            case KConstants.REPLICATIONBYTESINPERSEC:
                KafkaMonitorDTO replicationBytesOutPerSec = common(host, port, KafkaIndicatorName.REPLICATION_BYTES_IN_PER_SEC.getValue());
                if (replicationBytesOutPerSec != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(replicationBytesOutPerSec.getOneMinute()) + "");
                }
                break;
            case KConstants.REPLICATIONBYTESOUTPERSEC:
                KafkaMonitorDTO produceMessageConv = common(host, port, KafkaIndicatorName.REPLICATION_BYTES_OUT_PER_SEC.getValue());
                if (produceMessageConv != null) {
                    kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.0" : kpiInfoDTO.getValue()) + StrUtils.numberic(produceMessageConv.getOneMinute()) + "");
                }
                break;
            case KConstants.OSTOTALMEMORY:
                long totalMemory = getOSMemory(zkUrl, KafkaIndicatorName.TOTAL_PHYSICAL_MEMORY_SIZE.getValue());
                kpiInfoDTO.setValue(Long.parseLong(kpiInfoDTO.getValue() == null ? "0" : kpiInfoDTO.getValue()) + totalMemory + "");
                break;
            case KConstants.OSFREEMEMORY:
                long freeMemory = getOSMemory(zkUrl, KafkaIndicatorName.FREE_PHYSICAL_MEMORY_SIZE.getValue());
                kpiInfoDTO.setValue(Long.parseLong(kpiInfoDTO.getValue() == null ? "0" : kpiInfoDTO.getValue()) + freeMemory + "");
                break;
            case KConstants.CPUUSED:
                double cpu = getUsedCpuValue(host, port);
                kpiInfoDTO.setValue(StrUtils.numberic(kpiInfoDTO.getValue() == null ? "0.00" : kpiInfoDTO.getValue()) + cpu + "");
                break;
            default:
                break;
        }
    }

    /*
     *  根据topic获取logsize
     * */
    public static long getLogSizeByTopic(KafkaSourceDTO sourceDTO, String topic) {
        Properties defaultKafkaConfig = initProperties(sourceDTO);
        long realLogSize = 0L;
        KafkaConsumer<String, String> consumer=null;
        try {
            consumer= new KafkaConsumer<>(defaultKafkaConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            List<PartitionInfo> allPartitionInfo = consumer.partitionsFor(topic);
            for (PartitionInfo partitionInfo : allPartitionInfo) {
                partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
            }
            java.util.Map<TopicPartition, Long> endLogSize = consumer.endOffsets(partitions);
            java.util.Map<TopicPartition, Long> startLogSize = consumer.beginningOffsets(partitions);
            long endSumLogSize = 0L;
            long startSumLogSize = 0L;
            for (Map.Entry<TopicPartition, Long> entry : endLogSize.entrySet()) {
                endSumLogSize += entry.getValue();
            }
            for (Map.Entry<TopicPartition, Long> entry : startLogSize.entrySet()) {
                startSumLogSize += entry.getValue();
            }
            realLogSize = endSumLogSize - startSumLogSize;
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            destroyProperty();
        }
        return realLogSize;
    }

    /*
     *  根据topic获取Capacity
     * */
    public static long getCapacityByTopic(KafkaSourceDTO sourceDTO, String topic) {
        ZkUtils zkUtils = ZkUtils.apply(sourceDTO.getUrl(), KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        List<BrokersDTO> brokersByZk = getBrokersByZk(zkUtils);
        JMXConnector connector = null;
        Properties prop = initProperties(sourceDTO);
        // 获取kafka consumer
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(prop);
        List<PartitionInfo> partitionInfos = consumer.partitionsFor(topic);
        long tpSize = 0L;
        Boolean isOpenJMX = false;
        try {
            for (BrokersDTO brokersDTO : brokersByZk) {
                if (-1 != brokersDTO.getJmxPort()) {
                    isOpenJMX = true;
                }
                if (isOpenJMX) {
                    String jmxUrl = String.format(DEFAULT_EFAK_JMX_URI, brokersDTO.getHost(), brokersDTO.getJmxPort());
                    //连接节点的jmx 获取监控信息
                    connector = JMXFactoryUtils.connectWithTimeout(jmxUrl);
                    MBeanServerConnection mbeanConnection = connector.getMBeanServerConnection();
                    for (PartitionInfo partitionInfo : partitionInfos) {
                        String objectName = String.format(KAFKA_LOG_SIZE, topic, partitionInfo.partition());
                        Object size = mbeanConnection.getAttribute(new ObjectName(objectName), "Value");
                        tpSize += Long.parseLong(size.toString());
                    }


                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull(connector)) {
                try {
                    connector.close();
                } catch (IOException e) {
                    log.error("Close kafka used cpu value jmx connector has error, msg is " + e.getMessage());
                }
            }
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
        }

        return tpSize;
    }


    public static long getByteInByTopic(KafkaSourceDTO sourceDTO, String topic) {
        ZkUtils zkUtils = ZkUtils.apply(sourceDTO.getUrl(), KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        List<BrokersDTO> brokersByZk = getBrokersByZk(zkUtils);
        String mbean = KafkaIndicatorName.BYTES_IN_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
        long byteIn = 0L;
        for (BrokersDTO brokersDTO : brokersByZk) {
            KafkaMonitorDTO common = common(brokersDTO.getHost(), brokersDTO.getJmxPort(), mbean);
            String meanRate = common.getMeanRate();
            byteIn += new Double(Double.parseDouble(meanRate)).longValue();
        }
        if(Objects.nonNull(zkUtils)){
            zkUtils.close();
        }
        return byteIn;

    }

    public static long getByteOutByTopic(KafkaSourceDTO sourceDTO, String topic) {
        ZkUtils zkUtils = ZkUtils.apply(sourceDTO.getUrl(), KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        List<BrokersDTO> brokersByZk = getBrokersByZk(zkUtils);
        String mbean = KafkaIndicatorName.BYTES_OUT_PER_SEC.getValue() + TOPIC_CONCAT_CHARACTER + topic;
        long byteOut = 0L;
        for (BrokersDTO brokersDTO : brokersByZk) {
            KafkaMonitorDTO common = common(brokersDTO.getHost(), brokersDTO.getJmxPort(), mbean);
            String meanRate = common.getMeanRate();
            byteOut += new Double(Double.parseDouble(meanRate)).longValue();
        }
        if(Objects.nonNull(zkUtils)){
            zkUtils.close();
        }
        return byteOut;

    }

    private static void assembly(JSONArray assemblys, KpiInfoDTO kpi) {
        JSONObject object = new JSONObject();
        object.put("x", DateUtils.convertUnixTime(kpi.getTimespan(), "yyyy-MM-dd HH:mm"));
        object.put("y", kpi.getValue());
        assemblys.add(object);
    }

    public static List<OffsetInfoDTO> getTopicOffset(KafkaSourceDTO sourceDTO, String topicName, String groupName) {
        Properties prop = initProperties(sourceDTO);
        AdminClient client = AdminClient.create(prop);
        List<OffsetInfoDTO> targets = new ArrayList<OffsetInfoDTO>();
        try {
            Map<TopicPartition, Object> offsets = JavaConversions.mapAsJavaMap(client.listGroupOffsets(groupName));
            KafkaConsumer<String, String> consumer = new KafkaConsumer<>(prop);
            List<TopicPartition> partitions = new ArrayList<>();
            List<PartitionInfo> allPartitionInfo = consumer.partitionsFor(topicName);
            for (PartitionInfo partitionInfo : allPartitionInfo) {
                partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
            }
            java.util.Map<TopicPartition, Long> endLogSize = consumer.endOffsets(partitions);
            for (Map.Entry<TopicPartition, Long> entry : endLogSize.entrySet()) {
                if (topicName.equals(entry.getKey().topic())) {
                    OffsetInfoDTO offsetInfo = new OffsetInfoDTO();
                    int partition = entry.getKey().partition();
                    offsetInfo.setCreate(DateUtils.getDate());
                    offsetInfo.setModify(DateUtils.getDate());
                    offsetInfo.setLogSize(entry.getValue());
                    offsetInfo.setOffset(offsets.get(entry.getKey()) == null ? 0L : (Long) offsets.get(entry.getKey()));
                    offsetInfo.setLag(offsetInfo.getOffset() == -1 ? 0 : (offsetInfo.getLogSize() - offsetInfo.getOffset()));

//                    offsetInfo.setOwner(getKafkaOffsetOwner(topicOffset.getCluster(), topicOffset.getGroup(), topicOffset.getTopic(), partition).getOwners());
                    offsetInfo.setPartition(partition);
                    targets.add(offsetInfo);
                }
            }


        } catch (Exception e) {
            e.printStackTrace();
            return targets;
        } finally {
            client.close();
        }
        return targets;
    }

    public static OffsetInfoDTO getTopicMessageStatistics(KafkaSourceDTO sourceDTO, String topicName, String groupName) {
        Properties prop = initProperties(sourceDTO);
        AdminClient client = AdminClient.create(prop);
        OffsetInfoDTO offsetInfo = new OffsetInfoDTO();
        KafkaConsumer<String, String> consumer =null;
        try {
            consumer = new KafkaConsumer<>(prop);
            Map<TopicPartition, Object> offsets = JavaConversions.mapAsJavaMap(client.listGroupOffsets(groupName));

            List<TopicPartition> partitions = new ArrayList<>();
            List<PartitionInfo> allPartitionInfo = consumer.partitionsFor(topicName);
            for (PartitionInfo partitionInfo : allPartitionInfo) {
                partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
            }

            java.util.Map<TopicPartition, Long> endLogSize = consumer.endOffsets(partitions);
            long logsize = 0L;
            long offsetsize = 0L;
            for (Map.Entry<TopicPartition, Long> entry : endLogSize.entrySet()) {
                if (topicName.equals(entry.getKey().topic())) {
                    logsize += entry.getValue();
                    offsetsize += (long) (offsets.get(entry.getKey()) == null ? 0L : offsets.get(entry.getKey()));
                }
            }
            offsetInfo.setLogSize(logsize);
            offsetInfo.setOffset(offsetsize);
            offsetInfo.setLag(Math.abs(logsize - offsetsize));
            offsetInfo.setGroupId(groupName);
        } catch (Exception e) {
            e.printStackTrace();
            return offsetInfo;
        } finally {
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
            client.close();
        }
        return offsetInfo;
    }

    public static List<KafkaConsumerDTO> getGroupInfoByGroupId(KafkaSourceDTO sourceDTO, String groupName, String topicName) {
        Properties prop = initProperties(sourceDTO);
        AdminClient client = AdminClient.create(prop);
        List<KafkaConsumerDTO> targets = new ArrayList<>();
        ZkUtils zkUtils = ZkUtils.apply(sourceDTO.getUrl(), KafkaConsistent.SESSION_TIME_OUT,
                KafkaConsistent.CONNECTION_TIME_OUT, JaasUtils.isZkSecurityEnabled());
        ZkClient zkClient = zkUtils.zkClient();
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(prop);
        try {

            //groupName为空时根据topic获取partition信息
            if (StringUtils.isEmpty(groupName)) {

                return getGroupInfoByTopicName(zkUtils, zkClient, prop, consumer, topicName);
            }


            if (StringUtils.isEmpty(topicName)) {

                return getGroupInfoByGroupId(zkUtils, zkClient, prop, consumer, groupName);
            }


            return getGroupInfoByGroupIdAndTopic(zkUtils, zkClient, prop, consumer, groupName, topicName);


        } catch (Exception e) {
            e.printStackTrace();
            return targets;
        } finally {
            client.close();
            if(Objects.nonNull(zkClient)){
                zkClient.close();
            }
            if(Objects.nonNull(zkUtils)){
                zkUtils.close();
            }
        }
    }

    public static List<KafkaConsumerDTO> getGroupInfoByTopicName(ZkUtils zkUtils, ZkClient zkClient, Properties prop, KafkaConsumer<String, String> consumer, String topicName) {
        List<KafkaConsumerDTO> targets = new ArrayList<>();
        List<String> consumerGroupsIds = new ArrayList<>();
        kafka.admin.AdminClient adminClient = kafka.admin.AdminClient.create(prop);
        try {
            List<GroupOverview> groups = JavaConversions.seqAsJavaList(adminClient.listAllGroupsFlattened().toSeq());
            groups.forEach(group -> consumerGroupsIds.add(group.groupId()));
            List<TopicPartition> partitions = new ArrayList<>();
            for (String consumerGroupsId : consumerGroupsIds) {
                Map<TopicPartition, Object> offsets = JavaConversions.mapAsJavaMap(adminClient.listGroupOffsets(consumerGroupsId));
                for (TopicPartition topicPartition : offsets.keySet()) {
                    if (topicPartition.topic().equals(topicName)) {
                        for (TopicPartition partitionInfo : offsets.keySet()) {
                            partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
                        }
                        java.util.Map<TopicPartition, Long> endLogSize = consumer.endOffsets(partitions);
                        java.util.Map<TopicPartition, Long> startLogSize = consumer.beginningOffsets(partitions);


                        for (Map.Entry<TopicPartition, Long> entry : endLogSize.entrySet()) {


//                    offsetInfo.setOwner(getKafkaOffsetOwner(topicOffset.getCluster(), topicOffset.getGroup(), topicOffset.getTopic(), partition).getOwners());
                            long realLogSize = endLogSize.get(entry.getKey()).longValue() - startLogSize.get(entry.getKey()).longValue();
                            long l = offsets.get(entry.getKey()) == null ? 0L : (Long) offsets.get(entry.getKey());
                            String status = "";
                            if (l == 0) {
                                /**
                                 * logsize equal offsets,follow two states.<br>
                                 * 1. maybe application shutdown.<br>
                                 * 2. maybe application run, but producer rate equal
                                 * consumer rate.<br>
                                 */
                                if (realLogSize == 0) {
                                    status = "PENDING";
                                } else {
                                    status = "SHUTDOWN";
                                }
                            } else {
                                status = "RUNNING";
                            }
                            //获取topic的创建时间和修改时间
                            Stat stat = new Stat();
                            String test = zkUtils.getTopicPartitionPath(entry.getKey().topic(), entry.getKey().partition());
                            Object o = zkClient.readData(test, stat);
                            // 组装kafka consumer 信息
                            KafkaConsumerDTO kafkaConsumerDTO = KafkaConsumerDTO.builder()
                                    .groupId(consumerGroupsId)
                                    .topic(entry.getKey().topic())
                                    .partition(entry.getKey().partition())
                                    .currentOffset(l)
                                    .logEndOffset(entry.getValue())
                                    .lag(l == -1 ? 0 : (entry.getValue() - l))
                                    .status(status)
                                    .createTime(DateUtil.getFormattedDate(stat.getCtime(), "yyyy-MM-dd HH:mm"))
                                    .updateTime(DateUtil.getFormattedDate(stat.getMtime(), "yyyy-MM-dd HH:mm"))
                                    .owner("--")
                                    .build();
                            List<PartitionInfo> allPartitionInfo = consumer.partitionsFor(entry.getKey().topic());
                            for (PartitionInfo partitionInfo : allPartitionInfo) {
                                if (entry.getKey().partition() == partitionInfo.partition() && Objects.nonNull(partitionInfo.leader())) {
                                    kafkaConsumerDTO.setBrokerHost(partitionInfo.leader().host());
                                    break;
                                }
                            }

                            targets.add(kafkaConsumerDTO);

                        }
                    }

                }

            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            adminClient.close();
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
        }


        return targets;
    }

    public static List<KafkaConsumerDTO> getGroupInfoByGroupId(ZkUtils zkUtils, ZkClient zkClient, Properties prop, KafkaConsumer<String, String> consumer, String groupId) {
        List<KafkaConsumerDTO> targets = new ArrayList<>();
        List<String> consumerGroupsIds = new ArrayList<>();
        kafka.admin.AdminClient adminClient = kafka.admin.AdminClient.create(prop);
        try {
            List<GroupOverview> groups = JavaConversions.seqAsJavaList(adminClient.listAllGroupsFlattened().toSeq());
            groups.forEach(group -> consumerGroupsIds.add(group.groupId()));
            List<TopicPartition> partitions = new ArrayList<>();
            Map<TopicPartition, Object> offsets = JavaConversions.mapAsJavaMap(adminClient.listGroupOffsets(groupId));
            for (TopicPartition partitionInfo : offsets.keySet()) {
                partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
            }
            java.util.Map<TopicPartition, Long> endLogSize = consumer.endOffsets(partitions);
            java.util.Map<TopicPartition, Long> startLogSize = consumer.beginningOffsets(partitions);


            for (Map.Entry<TopicPartition, Long> entry : endLogSize.entrySet()) {


//                    offsetInfo.setOwner(getKafkaOffsetOwner(topicOffset.getCluster(), topicOffset.getGroup(), topicOffset.getTopic(), partition).getOwners());
                long realLogSize = endLogSize.get(entry.getKey()).longValue() - startLogSize.get(entry.getKey()).longValue();
                long l = offsets.get(entry.getKey()) == null ? 0L : (Long) offsets.get(entry.getKey());
                String status = "";
                if (l == 0) {
                    /**
                     * logsize equal offsets,follow two states.<br>
                     * 1. maybe application shutdown.<br>
                     * 2. maybe application run, but producer rate equal
                     * consumer rate.<br>
                     */
                    if (realLogSize == 0) {
                        status = "PENDING";
                    } else {
                        status = "SHUTDOWN";
                    }
                } else {
                    status = "RUNNING";
                }
                //获取topic的创建时间和修改时间
                Stat stat = new Stat();
                String test = zkUtils.getTopicPartitionPath(entry.getKey().topic(), entry.getKey().partition());
                Object o = zkClient.readData(test, stat);
                // 组装kafka consumer 信息
                KafkaConsumerDTO kafkaConsumerDTO = KafkaConsumerDTO.builder()
                        .groupId(groupId)
                        .topic(entry.getKey().topic())
                        .partition(entry.getKey().partition())
                        .currentOffset(l)
                        .logEndOffset(entry.getValue())
                        .lag(l == -1 ? 0 : (entry.getValue() - l))
                        .status(status)
                        .createTime(DateUtil.getFormattedDate(stat.getCtime(), "yyyy-MM-dd HH:mm"))
                        .updateTime(DateUtil.getFormattedDate(stat.getMtime(), "yyyy-MM-dd HH:mm"))
                        .owner("--")
                        .build();
                List<PartitionInfo> allPartitionInfo = consumer.partitionsFor(entry.getKey().topic());
                for (PartitionInfo partitionInfo : allPartitionInfo) {
                    if (entry.getKey().partition() == partitionInfo.partition() && Objects.nonNull(partitionInfo.leader())) {
                        kafkaConsumerDTO.setBrokerHost(partitionInfo.leader().host());
                        break;
                    }
                }

                targets.add(kafkaConsumerDTO);

            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            adminClient.close();
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
        }


        return targets;
    }


    public static List<KafkaConsumerDTO> getGroupInfoByGroupIdAndTopic(ZkUtils zkUtils, ZkClient zkClient, Properties prop, KafkaConsumer<String, String> consumer, String groupId, String topic) {
        List<KafkaConsumerDTO> targets = new ArrayList<>();
        List<String> consumerGroupsIds = new ArrayList<>();
        kafka.admin.AdminClient adminClient = kafka.admin.AdminClient.create(prop);
        try {
            List<GroupOverview> groups = JavaConversions.seqAsJavaList(adminClient.listAllGroupsFlattened().toSeq());
            groups.forEach(group -> consumerGroupsIds.add(group.groupId()));
            List<TopicPartition> partitions = new ArrayList<>();
            List<PartitionInfo> allPartitionInfo = consumer.partitionsFor(topic);
            for (PartitionInfo partitionInfo : allPartitionInfo) {
                partitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
            }
            java.util.Map<TopicPartition, Long> endLogSize = consumer.endOffsets(partitions);
            java.util.Map<TopicPartition, Long> startLogSize = consumer.beginningOffsets(partitions);

            Map<TopicPartition, Object> offsets = JavaConversions.mapAsJavaMap(adminClient.listGroupOffsets(groupId));
            for (Map.Entry<TopicPartition, Long> entry : endLogSize.entrySet()) {
                if (topic.equals(entry.getKey().topic())) {

//                    offsetInfo.setOwner(getKafkaOffsetOwner(topicOffset.getCluster(), topicOffset.getGroup(), topicOffset.getTopic(), partition).getOwners());
                    long realLogSize = endLogSize.get(entry.getKey()).longValue() - startLogSize.get(entry.getKey()).longValue();
                    long l = offsets.get(entry.getKey()) == null ? 0L : (Long) offsets.get(entry.getKey());
                    String status = "";
                    if (l == 0) {
                        /**
                         * logsize equal offsets,follow two states.<br>
                         * 1. maybe application shutdown.<br>
                         * 2. maybe application run, but producer rate equal
                         * consumer rate.<br>
                         */
                        if (realLogSize == 0) {
                            status = "PENDING";
                        } else {
                            status = "SHUTDOWN";
                        }
                    } else {
                        status = "RUNNING";
                    }
                    //获取topic的创建时间和修改时间
                    Stat stat = new Stat();
                    String test = zkUtils.getTopicPartitionPath(topic, entry.getKey().partition());
                    Object o = zkClient.readData(test, stat);
                    // 组装kafka consumer 信息
                    KafkaConsumerDTO kafkaConsumerDTO = KafkaConsumerDTO.builder()
                            .groupId(groupId)
                            .topic(topic)
                            .partition(entry.getKey().partition())
                            .currentOffset(l)
                            .logEndOffset(entry.getValue())
                            .lag(l == -1 ? 0 : (entry.getValue() - l))
                            .status(status)
                            .createTime(DateUtil.getFormattedDate(stat.getCtime(), "yyyy-MM-dd HH:mm"))
                            .updateTime(DateUtil.getFormattedDate(stat.getMtime(), "yyyy-MM-dd HH:mm"))
                            .owner("--")
                            .build();
                    for (PartitionInfo partitionInfo : allPartitionInfo) {
                        if (entry.getKey().partition() == partitionInfo.partition() && Objects.nonNull(partitionInfo.leader())) {
                            kafkaConsumerDTO.setBrokerHost(partitionInfo.leader().host());
                            break;
                        }
                    }

                    targets.add(kafkaConsumerDTO);
                }


            }
        }catch (Exception e){
            adminClient.close();
            if(Objects.nonNull(consumer)){
                consumer.close();
            }
        }

        return targets;
    }


}
