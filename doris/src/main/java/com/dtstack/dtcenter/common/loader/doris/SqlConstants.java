package com.dtstack.dtcenter.common.loader.doris;

public class SqlConstants {

    //函数常量
    public static final String FUNCTION_TYPE = "FUNCTION";
    //存储过程
    public static final String  PROCEDURE_TYPE = "PROCEDURE";
    //表
    public static final String TABLE_TYPE = "TABLE";
    //视图
    public static final String VIEW_TYPE = "VIEW";
    // 视图
    public static final String VIEW = "'VIEW'";

    // 普通表
    public static final String BASE_TABLE = "'BASE TABLE'";

    // 获取指定数据库下的表
    public static final String GET_TABLE_SCHEMA_SQL = "select t.table_schema,t.table_name,t.table_type,t.table_comment,COUNT(c.COLUMN_NAME) AS column_count from information_schema.tables t LEFT JOIN  information_schema.columns c ON t.TABLE_SCHEMA = c.TABLE_SCHEMA  AND t.TABLE_NAME = c.TABLE_NAME  where t.table_schema='%s' and t.table_type = %s  %s";

    //表名模糊搜索sql
    // 表名正则匹配模糊查询
    public static final String SEARCH_SQL = " AND t.table_name LIKE  '%%%s%%'  ";

    //分组 sql
    public static final String GROUP_SQL = " GROUP BY t.TABLE_SCHEMA,t.TABLE_NAME,t.table_type,t.table_comment";

    // 获取指定数据库下的表—包括视图、表
    public static final String GET_TABLE_SCHEMA_SQL_ALL = "select t.table_schema,t.table_name,t.table_type, t.table_comment, COUNT(c.COLUMN_NAME) AS column_count from  information_schema.tables t LEFT JOIN  information_schema.columns c ON t.TABLE_SCHEMA = c.TABLE_SCHEMA  AND t.TABLE_NAME = c.TABLE_NAME where t.table_schema='%s' and t.table_type in ('BASE TABLE','VIEW')  %s";

    //获取指定库下或者指定表下的索引
    public static final String GET_INDEX_SQL = "\n" +
            "select\n" +
            "\tTABLE_SCHEMA ,\n" +
            "    INDEX_NAME AS index_name,\n" +
            "    TABLE_NAME AS table_name,\n" +
            "    NON_UNIQUE AS is_unique,\n" +
            "    INDEX_TYPE AS index_type,\n" +
            "    INDEX_COMMENT AS index_comment,\n" +
            "    COUNT(COLUMN_NAME) AS column_count\n" +
            "FROM INFORMATION_SCHEMA.STATISTICS\n" +
            "WHERE TABLE_SCHEMA = '%s'\n";

    /**
     * 索引搜索sql
     */
    public static final String SEARCH_INDEX_SQL = " AND INDEX_NAME LIKE  '%%%s%%'  ";

    /**
     * 索引分组sql、
     */
    public static final String GROUP_INDEX_SQL = " GROUP BY TABLE_SCHEMA,TABLE_NAME,INDEX_NAME,NON_UNIQUE,INDEX_TYPE,INDEX_COMMENT";





    //获取指定库下指定表的索引
    public static final String GET_INDEX_SQL_BY_TABLE = "select\n" +
            "\tTABLE_SCHEMA ,\n" +
            "    INDEX_NAME AS index_name,\n" +
            "    TABLE_NAME AS table_name,\n" +
            "    NON_UNIQUE AS is_unique,\n" +
            "    INDEX_TYPE AS index_type,\n" +
            "    INDEX_COMMENT AS index_comment,\n" +
            "    COUNT(COLUMN_NAME) AS column_count\n" +
            "FROM INFORMATION_SCHEMA.STATISTICS\n" +
            "WHERE TABLE_SCHEMA = '%s'\n" +
            "and  TABLE_NAME='%s'\n";

    //获取索引字段列sql
    public static final String GET_INDEX_COLUMN_SQL = "select\n" +
            "\t INDEX_NAME ,\n" +
            "\t TABLE_NAME ,\n" +
            "\t COLUMN_NAME ,\n" +
            "    SEQ_IN_INDEX ,\n" +
            "    collation,\n" +
            "    NULLABLE \n" +
            "FROM INFORMATION_SCHEMA.STATISTICS\n" +
            "WHERE TABLE_SCHEMA = '%s'\n" +
            "and  TABLE_NAME='%s'\n" +
            "and INDEX_NAME='%s'" ;


    //获取指定库得函数或者存储过程
    public static final String GET_PRODUCE_SQL = "SELECT\n" +
            "\tROUTINE_SCHEMA,\n" +
            "\tROUTINE_NAME,\n" +
            "\tROUTINE_TYPE,\n" +
            "\tROUTINE_COMMENT,\n" +
            "\tROUTINE_BODY AS ROUTINE_DEFINITION,\n" +
            "\tCHARACTER_SET_CLIENT,\n" +
            "\tCOLLATION_CONNECTION,\n" +
            "\tEXTERNAL_LANGUAGE\n" +
            "FROM\n" +
            "\tINFORMATION_SCHEMA.ROUTINES \n" +
            "WHERE\n" +
            "\tROUTINE_SCHEMA = '%s' \n" +
            "\tAND ROUTINE_TYPE = '%s'" ;

    public static final String SEARCH_PRODUCE_SQL = " AND ROUTINE_NAME LIKE  '%%%s%%'  ";

    //获取指定库得函数或者存储过程参数
    public static final String GET_PRODUCE_ARGUMENTS_SQL = "SELECT\n" +
            "\tSPECIFIC_SCHEMA,\n" +
            "    SPECIFIC_NAME ,\n" +
            "    PARAMETER_NAME ,\n" +
            "    DATA_TYPE ,\n" +
            "    PARAMETER_MODE ,\n" +
            "    ORDINAL_POSITION,\n" +
            "    NUMERIC_PRECISION,\n" +
            "    NUMERIC_SCALE,\n" +
            "    CHARACTER_OCTET_LENGTH\n" +
            "FROM INFORMATION_SCHEMA.PARAMETERS\n" +
            "WHERE SPECIFIC_SCHEMA = '%s'\n" +
            "  AND SPECIFIC_NAME = '%s'" ;
}
