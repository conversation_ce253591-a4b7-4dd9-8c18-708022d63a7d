package com.dtstack.dtcenter.common.loader.saphana;

public class SqlConstants {

    //函数常量
    public static final String FUNCTION_TYPE = "FUNCTION";
    //存储过程
    public static final String  PROCEDURE_TYPE = "PROCEDURE";
    //表
    public static final String TABLE_TYPE = "TABLE";
    //视图
    public static final String VIEW_TYPE = "VIEW";
    // 视图
    public static final String VIEW = "'VIEW'";

    // 普通表
    public static final String BASE_TABLE = "'BASE TABLE'";

    // 获取指定数据库下的表
    public static final String GET_TABLE_SCHEMA_SQL = " SELECT  TABLE_NAME AS NAME, SCHEMA_NAME FROM SYS.TABLES WHERE SCHEMA_NAME = '%s' ";
    // 获取全部视图
    public static final String GET_VIEW_SCHEMA_SQL = " SELECT VIEW_NAME AS NAME ,SCHEMA_NAME  FROM SYS.VIEWS WHERE SCHEMA_NAME = '%s'  ";

    //表名模糊搜索sql
    // 表名正则匹配模糊查询
    public static final String SEARCH_SQL = " AND NAME LIKE  '%%%s%%'  ";


    //获取指定库下或者指定表下的索引
    public static final String GET_INDEX_SQL = "SELECT \n" +
            "    SCHEMA_NAME AS TABLE_SCHEMA,\n" +
            "    INDEX_NAME AS index_name,\n" +
            "    TABLE_NAME AS table_name,\n" +
            "    \"CONSTRAINT\"  ,\n" +
            "    INDEX_TYPE AS index_type\n" +
            "FROM SYS.INDEXES\n" +
            "WHERE SCHEMA_NAME = '%s'";

    /**
     * 索引搜索sql
     */
    public static final String SEARCH_INDEX_SQL = " AND INDEX_NAME LIKE  '%%%s%%'  ";





    //获取指定库下指定表的索引
    public static final String GET_INDEX_SQL_BY_TABLE = "select\n" +
            "\tTABLE_SCHEMA ,\n" +
            "    INDEX_NAME AS index_name,\n" +
            "    TABLE_NAME AS table_name,\n" +
            "    NON_UNIQUE AS is_unique,\n" +
            "    INDEX_TYPE AS index_type,\n" +
            "    INDEX_COMMENT AS index_comment,\n" +
            "    COUNT(COLUMN_NAME) AS column_count\n" +
            "FROM INFORMATION_SCHEMA.STATISTICS\n" +
            "WHERE TABLE_SCHEMA = '%s'\n" +
            "and  TABLE_NAME='%s'\n";

    //获取索引字段列sql
    public static final String GET_INDEX_COLUMN_SQL = "SELECT \n" +
            "    INDEX_NAME,\n" +
            "    TABLE_NAME,\n" +
            "    COLUMN_NAME,\n" +
            "    POSITION AS SEQ_IN_INDEX,\n" +
            "    ASCENDING_ORDER AS collation\n" +
            "FROM SYS.INDEX_COLUMNS\n" +
            "WHERE SCHEMA_NAME = '%s'\n" +
            "  AND TABLE_NAME = '%s'\n" +
            "  AND INDEX_NAME = '%s'" ;


    //获取指定库得函数或者存储过程
    public static final String GET_FUNCTION_SQL = "SELECT \n" +
            "    SCHEMA_NAME AS ROUTINE_SCHEMA,\n" +
            "    FUNCTION_NAME AS ROUTINE_NAME,\n" +
            "    'FUNCTION' AS ROUTINE_TYPE,\n" +
            "    DEFINITION AS ROUTINE_DEFINITION\n" +
            "FROM SYS.FUNCTIONS\n" +
            "WHERE SCHEMA_NAME = '%s'" ;

    public static final String GET_PRODUCE_SQL = " " +
            "SELECT \n" +
            "    SCHEMA_NAME AS ROUTINE_SCHEMA,\n" +
            "    PROCEDURE_NAME AS ROUTINE_NAME,\n" +
            "    'PROCEDURE' AS ROUTINE_TYPE,\n" +
            "    DEFINITION AS ROUTINE_DEFINITION\n" +
            "FROM SYS.PROCEDURES\n" +
            "WHERE SCHEMA_NAME = '%s'" ;

    public static final String SEARCH_PRODUCE_SQL = " AND ROUTINE_NAME LIKE  '%%%s%%'  ";

    //获取指定库得函数或者存储过程参数
    public static final String GET_PRODUCE_ARGUMENTS_SQL = "SELECT\n" +
            "    SCHEMA_NAME AS SPECIFIC_SCHEMA,\n" +
            "    PROCEDURE_NAME AS SPECIFIC_NAME,\n" +
            "    PARAMETER_NAME,\n" +
            "    DATA_TYPE_NAME AS DATA_TYPE,\n" +
            "    PARAMETER_TYPE AS PARAMETER_MODE,\n" +
            "    \"POSITION\" AS ORDINAL_POSITION,\n" +
            "    SCALE AS NUMERIC_SCALE,\n" +
            "    LENGTH AS CHARACTER_OCTET_LENGTH,\n" +
            "    HAS_DEFAULT_VALUE,\n" +
            "    IS_NULLABLE " +
            "FROM SYS.PROCEDURE_PARAMETERS\n" +
            "WHERE SCHEMA_NAME = '%s'\n" +
            "  AND PROCEDURE_NAME = '%s'" ;
    public static final String GET_MAX_CONNECTIONS = "SELECT value AS max_connections FROM SYS.M_SYSTEM_LIMITS  WHERE NAME = 'MAXIMUM_NUMBER_OF_SESSIONS' ";


    public static final String GET_METADATA_PRIVILEGES = "SELECT USER_NAME FROM SYS.USERS LIMIT 1 ";

    public static final String SHOW_GRANTS = "SELECT PRIVILEGE  FROM SYS.GRANTED_PRIVILEGES WHERE GRANTEE = CURRENT_USER ";

    /**
     * 获取函数ddl
     */

    public static final String FUNCTION_CREATE_SQL = "SELECT \n" +
            "DEFINITION AS DDL " +
            "FROM SYS.FUNCTIONS " +
            "WHERE SCHEMA_NAME = '%s' AND FUNCTION_NAME= '%s' " ;
    public static final String PROCEDURE_CREATE_SQL = " " +
            "SELECT \n" +
            " DEFINITION AS DDL " +
            "FROM SYS.PROCEDURES\n" +
            "WHERE SCHEMA_NAME = '%s' AND PROCEDURE_NAME= '%s' " ;

}
