package com.dtstack.dtcenter.loader.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * sql里面传入最基本的where拼接条件
 *
 * <AUTHOR>
 * @date 2023/6/1 14:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RelationConditionDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 关联字段
     */
    private String columnName;

    /**
     * 关联值
     */
    private String columnValue;

    /**
     * 关联字段类型
     */
    private String columnType;
}
