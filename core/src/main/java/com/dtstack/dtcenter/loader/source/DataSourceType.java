/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.source;

import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 10:32 2020/7/27
 * @Description：数据源类型 值 1000 以上表示未启用，后续标号 99 以下倒序的表示定制化的需求
 */
public enum DataSourceType {
    // RDBMS
    MySQL(1, 0, "MySQL", "mysql5"),
    MySQL8(1001, 1, "MySQL", "mysql8"),
    MySQL8_HIVE(2024, 1, "MYSQL8_HIVE", "mysql8_hive"),
    MySQL5_HIVE(2025, 1, "MYSQL5_HIVE", "mysql5_hive"),
    MySQLPXC(98, 1, "MySQL PXC", "mysql5"),
    Polardb_For_MySQL(28, 2, "PolarDB for MySQL8", "mysql5"),
    Oracle(2, 3, "Oracle", "oracle"),
    Oracle_9i(2014, 3, "Oracle", "oracle_9i"),
    Oracle_19c(2015, 3, "Oracle", "oracle_19c"),
    SQLServer(3, 4, "SQLServer", "sqlServer"),
    SQLSERVER_2017_LATER(32, 5, "SQLServer JDBC", "sqlServer2017"),
    PostgreSQL(4, 6, "PostgreSQL", "postgresql"),
    TBase(2018, 6, "TBase", "tbase"),
    DWS_PG(2008, 6, "DWS_PG", "dws_pg"),
    DWS_MySQL(2009, 6, "DWS_MySQL", "dws_mysql"),
    DB2(19, 7, "DB2", "db2"),
    DB2_AS400(1009, 7, "DB2_AS400", "db2_as400"),
    DMDB(35, 8, "DMDB For MySQL", "dmdb"),
    RDBMS(5, 9, "RDBMS", "mysql"),
    KINGBASE8(40, 10, "KingbaseES8", "kingbase8"),
    DMDB_For_Oracle(67, 8, "DMDB For Oracle", "dmdb"),
    MariaDB(63,100,"MariaDB","mariadb"),
    KunDB(1004,148,"KunDB","kundb"),
    Sequoiadb_FOR_MYSQL(1005, 152, "Sequoiadb_FOR_MYSQL", "sequoiadb_for_mysql"),

    // Hadoop
    HIVE(7, 20, "Hive2.x", "hive"),
    HIVE1X(27, 21, "Hive1.x", "hive1"),
    HIVE3X(50, 22, "Hive3.x", "hive3"),
    HIVE3X_MRS(74, 23, "Hive3.x.mrs", "huawei_hive3"),
    MAXCOMPUTE(10, 23, "MaxCompute", "maxcompute"),

    // MPP
    GREENPLUM6(36, 40, "Greenplum", "greenplum6"),
    LIBRA(21, 41, "GaussDB", "libra"),
    GBase_8a(22, 42, "GBase_8a", "gbase"),
    DORIS(57, 43, "doris0.x", "doris"),
    DORIS1(2016, 43, "doris1.x", "doris"),
    DORIS2(2017, 43, "doris2.x", "doris"),

    // FileSystem
    HDFS(6, 60, "HDFS", "hdfs"),
    HDFS_MRS(2021, 60, "HDFS_MRS", "huawei_hdfs"),
    // FileSystem
    HDFS3(69, 69, "HDFS3", "hdfs3"),
    FTP(9, 61, "FTP", "ftp"),
    SFTP(2005, 61, "SFTP", "ftp"),
    HOST(1008, 61, "HOST", "ftp"),
    S3(41, 62, "S3", "s3"),
    AWS_S3(51, 63, "AWS S3", "aws_s3"),
    OSS_ALI(2006, 63, "OSS_ALI", "oss_ali"),
    DATA_HUB(2013, 63, "datahub", "datahub"),
    OSS_HUAWEI(2011, 63, "OSS_HUAWEI", "oss_huawei"),
    OSS_LC(2010, 63, "OSS_LC", "oss_lc"),

    StarRocks(72, 149, "StarRocks", "starRocks"),
    GoldenDB(73, 151, "GoldenDB", "goldendb"),

    // Analytic
    SparkThrift2_1(45, 80, "SparkThrift2.x", "spark"),
    IMPALA(29, 81, "Impala", "impala"),
    Clickhouse(25, 82, "ClickHouse", "clickhouse"),
    Clickhouse_mrs(2023, 82, "ClickHouse_MRS", "huawei_clickhouse"),
    TiDB(31, 83, "TiDB", "tidb"),
    CarbonData(20, 84, "CarbonData", "hive"),
    Kudu(24, 85, "Kudu", "kudu"),
    ADS(15, 86, "AnalyticDB_MySQL", "analyticdb_mysql"),
    ADB_FOR_PG(54, 87, "AnalyticDB_PostgreSQL", "adb_postgresql"),
    Kylin(23, 88, "Kylin", "kylin"),
    Presto(48, 89, "Presto", "presto"),
    OceanBase(49, 90, "For Mysql", "oceanbase"),
    OceanBase_FOR_ORACLE(1006, 150, "For Oracle", "oceanbase_for_oracle"),
    INCEPTOR(52, 91, "Inceptor", "inceptor"),
    INCEPTOR8(2012, 91, "Inceptor", "inceptor8"),
    TRINO(59, 92, "Trino", "trino"),
    SAP_HANA1(76, 93, "SAP HANA 1.x", "sap_hana"),
    SAP_HANA2(77, 94, "SAP HANA 2.x", "sap_hana"),

    // NoSQL
    HBASE(8, 100, "HBase", "hbase"),
    HBASE2(39, 101, "HBase2", "hbase2"),
    HBASE_MRS(2022, 101, "HBASE_MRS", "huawei_hbase"),
    Phoenix(30, 102, "Phoenix4.x", "phoenix"),
    PHOENIX5(38, 103, "Phoenix5.x", "phoenix5"),
    ES(11, 104, "ElasticSearch5.x", "es5"),
    ES6(33, 105, "ElasticSearch6.x", "es"),
    ES7(46, 106, "ElasticSearch7.x", "es7"),
    ES8(2033, 106, "ElasticSearch8.x", "es8"),
    MONGODB(13, 107, "MongoDB", "mongo"),
    REDIS(12, 108, "Redis", "redis"),
    SOLR(53, 109, "Solr", "solr"),
    //FIXME 临时增加，适配gateway上线，排除hadoop和hbase依赖，下版本删除
    HBASE_GATEWAY(99, 109, "HBase1.x", "hbase_gateway"),

    // others
    KAFKA_2X(37, 120, "Kafka2.x", "kafka"),
    KAFKA(26, 121, "Kafka", "kafka"),
    KAFKA_11(14, 122, "Kafka_0.11", "kafka"),
    KAFKA_10(17, 123, "Kafka_0.10", "kafka"),
    KAFKA_09(18, 124, "Kafka_0.9", "kafka"),
    EMQ(34, 125, "EMQ", "emq"),
    WEB_SOCKET(42, 126, "WebSocket", "websocket"),
    SOCKET(44, 127, "Socket", "socket"),
    RESTFUL(47, 128, "Restful", "restful"),
    VERTICA(43, 129, "Vertica", "vertica"),
    INFLUXDB(55, 130, "InfluxDB", "influxdb"),
    OPENTSDB(56, 131, "OpenTSDB", "opentsdb"),
    BEATS(16, 132, "Beats", "null"),
    Spark(1002, 133, "Spark", "spark"),
    KylinRestful(58, 135, "KylinRestful", "kylinrestful"),
    TDSQL_FOR_MySQL(1007, 0, "TDSQL_FOR_MySQL", "tdsql_for_mysql"),
    TDSQL_FOR_PG(2019, 0, "TDSQL_FOR_PG", "tdsql_for_pg"),
    TDSQL_FOR_ORACLE(2020, 0, "TDSQL_FOR_ORACLE", "tdsql_for_oracle"),

    TBDS_HDFS(60, 136, "TBDS_HDFS", "tbds_hdfs"),
    TBDS_HBASE(61, 137, "TBDS_HBASE", "tbds_hbase"),
    TBDS_KAFKA(62, 138, "TBDS_KAFKA", "tbds_kafka"),
    DorisRestful(64, 139, "DorisRestful", "dorisrestful"),
    HIVE3_CDP(65, 140, "Hive3_CDP", "hive3_cdp"),
    HUAWEI_KAFKA(70, 143, "HUAWEI_KAFKA", "huawei_kafka"),
    HIGH_VERSION_KAFKA(68, 145, "HIGH_VERSION_KAFKA", "high_version_kafka"),
    Informix(1003, 3, "Informix", "informix"),
    GaussDB(2000, 147, "GaussDB", "gaussdb"),
    GaussDB_HIVE(2026, 147, "GaussDB_HIVE", "gaussdb_hive"),
    Gauss_DB200(2007, 147, "Gauss_DB200", "gauss_db200"),
    GaussDB_FOR_MySQL(2003, 147, "GaussDB_FOR_MySQL", "gaussdb_for_mysql"),
    ArgoDB(2002, 153, "ArgoDB", "argodb"),
    GP_FOR_PG(66, 87, "Greenplum_PostgreSQL", "greenplum_postgresql"),

    // Sybase
    Sybase_jTDS(2030, 160, "Sybase_jTDS", "sybase_jtds"),
    Sybase_jConnect(2031, 161, "Sybase_jConnect", "sybase_jconnect"),
    INSPUR_S3(2032, 63, "INSPUR_S3", "inspur_s3"),
    ;

    DataSourceType(int val, int order, String name, String pluginName) {
        this.val = val;
        this.order = order;
        this.name = name;
        this.pluginName = pluginName;
    }

    private static final List<Integer> RDBM_S = new ArrayList<>();
    private static final List<Integer> KAFKA_S = new ArrayList<>();

    static {
        RDBM_S.add(MariaDB.val);
        RDBM_S.add(KunDB.val);
        RDBM_S.add(MySQL.val);
        RDBM_S.add(MySQL8.val);
        RDBM_S.add(MySQLPXC.val);
        RDBM_S.add(Polardb_For_MySQL.val);
        RDBM_S.add(Oracle.val);
        RDBM_S.add(SQLServer.val);
        RDBM_S.add(SQLSERVER_2017_LATER.val);
        RDBM_S.add(PostgreSQL.val);
        RDBM_S.add(DB2.val);
        RDBM_S.add(DMDB.val);
        RDBM_S.add(RDBMS.val);
        RDBM_S.add(HIVE.val);
        RDBM_S.add(HIVE1X.val);
        RDBM_S.add(HIVE3X.val);
        RDBM_S.add(HIVE3_CDP.val);
        RDBM_S.add(HIVE3X_MRS.val);
        RDBM_S.add(Spark.val);
        RDBM_S.add(SparkThrift2_1.val);
        RDBM_S.add(Presto.val);
        RDBM_S.add(Kylin.val);
        RDBM_S.add(VERTICA.val);
        RDBM_S.add(GREENPLUM6.val);
        RDBM_S.add(LIBRA.val);
        RDBM_S.add(GBase_8a.val);
        RDBM_S.add(Clickhouse.val);
        RDBM_S.add(Clickhouse_mrs.val);
        RDBM_S.add(TiDB.val);
        RDBM_S.add(CarbonData.val);
        RDBM_S.add(ADS.val);
        RDBM_S.add(ADB_FOR_PG.val);
        RDBM_S.add(Phoenix.val);
        RDBM_S.add(PHOENIX5.val);
        RDBM_S.add(IMPALA.val);
        RDBM_S.add(OceanBase.val);
        RDBM_S.add(OceanBase_FOR_ORACLE.val);
        RDBM_S.add(INCEPTOR.val);
        RDBM_S.add(KINGBASE8.val);
        RDBM_S.add(TRINO.val);
        RDBM_S.add(DMDB_For_Oracle.val);
        RDBM_S.add(SAP_HANA1.val);
        RDBM_S.add(SAP_HANA2.val);
        RDBM_S.add(StarRocks.val);
        RDBM_S.add(TDSQL_FOR_MySQL.val);
        RDBM_S.add(GoldenDB.val);
        RDBM_S.add(Informix.val);
        RDBM_S.add(GaussDB.val);
        RDBM_S.add(Sequoiadb_FOR_MYSQL.val);
        RDBM_S.add(HIVE3X_MRS.val);
        RDBM_S.add(ArgoDB.val);
        RDBM_S.add(GP_FOR_PG.val);
        RDBM_S.add(GaussDB_FOR_MySQL.val);
        RDBM_S.add(Gauss_DB200.val);
        RDBM_S.add(DWS_MySQL.val);
        RDBM_S.add(DWS_PG.val);
        RDBM_S.add(Oracle_19c.val);
        RDBM_S.add(Oracle_9i.val);
        RDBM_S.add(TBase.val);
        RDBM_S.add(TDSQL_FOR_PG.val);
        RDBM_S.add(TDSQL_FOR_ORACLE.val);
        RDBM_S.add(Sybase_jTDS.val);
        RDBM_S.add(Sybase_jConnect.val);
        //doris
        RDBM_S.add(DORIS.val);
        RDBM_S.add(DORIS1.val);
        RDBM_S.add(DORIS2.val);

        KAFKA_S.add(KAFKA.val);
        KAFKA_S.add(KAFKA_09.val);
        KAFKA_S.add(KAFKA_10.val);
        KAFKA_S.add(KAFKA_11.val);
        KAFKA_S.add(KAFKA_2X.val);
        KAFKA_S.add(TBDS_KAFKA.val);
        KAFKA_S.add(HUAWEI_KAFKA.val);
        KAFKA_S.add(HIGH_VERSION_KAFKA.val);
    }

    /**
     * 数据源值
     */
    private int val;

    /**
     * 排序顺序
     */
    private int order;

    /**
     * 数据源名称
     */
    private String name;

    private String pluginName;

    /**
     * 根据值获取数据源类型
     *
     * @param value
     * @return
     */
    public static @NotNull DataSourceType getSourceType(int value) {
        for (DataSourceType type : DataSourceType.values()) {
            if (type.val == value) {
                return type;
            }
        }

        throw new DtLoaderException("Data source type is not supported");
    }

    public Integer getVal() {
        return val;
    }

    public String getName() {
        return name;
    }

    public String getPluginName() {
        return pluginName;
    }

    public int getOrder() {
        return order;
    }

    /**
     * 获取所有的关系数据库
     *
     * @return
     */
    public static List<Integer> getRDBMS() {
        return RDBM_S;
    }

    /**
     * 获取所有的 kafka 相关数据源
     *
     * @return
     */
    public static List<Integer> getKafkaS() {
        return KAFKA_S;
    }

    /**
     * 用来计算未使用的最小数据源值
     *
     * @param args
     */
    public static void main(String[] args) {
        List<DataSourceType> collect = Arrays.stream(DataSourceType.values()).sorted(Comparator.comparingInt(DataSourceType::getVal)).collect(Collectors.toList());
        int val = 1;
        for (DataSourceType dataSourceType : collect) {
            val = val == dataSourceType.getVal() - 1 ? dataSourceType.getVal() : val;
        }
        System.out.println("Sys.out.currentVal : " + (val + 1));
    }
}