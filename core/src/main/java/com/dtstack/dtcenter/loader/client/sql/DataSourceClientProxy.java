/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.sql;

import com.dsg.database.datasource.vo.DbTableVO;
import com.dsg.database.datasource.dto.*;
import com.dtstack.dtcenter.loader.ClassLoaderCallBackMethod;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.downloader.DownloaderProxy;
import com.dtstack.dtcenter.loader.dto.*;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 16:19 2020/1/6
 * @Description 代理实现
 */
@Slf4j
public class DataSourceClientProxy<T> implements IClient<T> {
    private IClient targetClient;

    public DataSourceClientProxy(IClient targetClient) {
        this.targetClient = targetClient;
    }

    @Override
    public Connection getCon(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCon(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Connection getCon(ISourceDTO source, String taskParams) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCon(source, taskParams),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public ConnectorResultEntity getResultSetStream(ISourceDTO source, SqlQueryDTO queryDTO, boolean enableStream, boolean autoCommit) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getResultSetStream(source, queryDTO, enableStream, autoCommit),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Boolean testCon(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.testCon(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<Map<String, Object>> executeQuery(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.executeQuery(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Boolean executeSqlWithoutResultSet(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.executeSqlWithoutResultSet(source,
                queryDTO), targetClient.getClass().getClassLoader());
    }

    @Override
    public List<JdbcSqlMetadataInfoDTO> executeSqlForMetadataInfo(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.executeSqlForMetadataInfo(source,
                queryDTO), targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> getTableList(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTableList(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTableAndViewList(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getCharacterSet(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCharacterSet(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Long getTableRows(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTableRows(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCharacterCollation(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> getTableListBySchema(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTableListBySchema(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> getColumnClassInfo(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getColumnClassInfo(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getColumnMetaData(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaDataWithSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getColumnMetaDataWithSql(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<ColumnMetaDTO> getFlinkColumnMetaData(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getFlinkColumnMetaData(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getTableMetaComment(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTableMetaComment(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getPreview(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public int getPreviewRows(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getPreviewRows(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String dealSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.dealSql(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> new DownloaderProxy(targetClient.getDownloader(source, queryDTO)),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, String sql, Integer pageSize) throws Exception {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> new DownloaderProxy(targetClient.getDownloader(source, sql, pageSize)),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> getAllDatabases(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getAllDatabases(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }
    @Override
    public List<String> getAllDbs(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getAllDbs(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> getRootDatabases(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getRootDatabases(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCreateTableSql(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getPartitionColumn(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Table getTable(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTable(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getCurrentDatabase(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCurrentDatabase(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getCurrentSchema(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCurrentSchema(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Boolean createDatabase(ISourceDTO source, String dbName, String comment) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.createDatabase(source, dbName, comment),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.isDatabaseExists(source, dbName),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Boolean isTableExistsInDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.isTableExistsInDatabase(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> getCatalogs(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCatalogs(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getVersion(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getVersion(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> listFileNames(ISourceDTO sourceDTO, String path, Boolean includeDir, Boolean recursive, Integer maxNum, String regexStr) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.listFileNames(sourceDTO, path, includeDir, recursive, maxNum, regexStr),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Database getDatabase(ISourceDTO sourceDTO, String dbName) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getDatabase(sourceDTO, dbName),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public TableInfo getTableInfo(ISourceDTO sourceDTO, String tableName) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTableInfo(sourceDTO, tableName),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getCharacterSetByDatabase(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCharacterSetByDatabase(sourceDTO, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getTimeZoneByDatabase(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTimeZoneByDatabase(sourceDTO, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public ColumnMetaDTO getDataType(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getDataType(sourceDTO, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public DatasourceInfoDTO getDataSourceImport(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getDataSourceImport(sourceDTO, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String dealWhereSql(ISourceDTO source, String previewSql, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.dealWhereSql(source, previewSql, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public ProcedureMetadata getProduce(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getProduce(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<ProcedureMetadataArguments> getProduceArguments(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getProduceArguments(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getTableLabel(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTableLabel(sourceDTO, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getUrl(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getUrl(sourceDTO,queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getMedataDataTables(sourceDTO,queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> isTablesExists(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.isTablesExists(sourceDTO,queryDTO),
                targetClient.getClass().getClassLoader());
    }
    @Override
    public  List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getIndexList(sourceDTO,queryDTO),
                targetClient.getClass().getClassLoader());
    }
    @Override
    public   List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getIndexColumn(sourceDTO,queryDTO),
                targetClient.getClass().getClassLoader());
    }
    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getFunctionList(sourceDTO,queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getFunctionArguments(sourceDTO,queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Integer getMaxConnections(ISourceDTO sourceDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getMaxConnections(sourceDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Boolean getMetadataPrivileges(ISourceDTO sourceDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getMetadataPrivileges(sourceDTO),
                targetClient.getClass().getClassLoader());
    }
}
