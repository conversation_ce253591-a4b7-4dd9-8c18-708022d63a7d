package com.dsg.database.datasource.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 元数据 业务对象 metadata_table
 *
 * <AUTHOR>
 * @date 2022-07-13
 */
@Data
public class MetadataChangeTable {
    private static final long serialVersionUID = 1L;

    /**
     * 元数据唯一标识 根据规则生成
     */
    private String metadataTableUuid;

    /**
     * 数据源类型
     */
    private String datasourceType;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 库名
     */
    private String databaseName;

    /**
     * 表中文名称
     */
    private String tableCnName;

    /**
     * 表别名
     */
    private String tableAlias;

    /**
     * 表类型
     */
    private String tableType;

    /**
     * 表注释
     */
    private String tableComment;

    /**
     * 表字符集
     */
    private String tableCharacter;

    /**
     * 字符排序规则
     */
    private String tableSortCharacter;

    /**
     * 表标签
     */
    private String tableLabel;

    /**
     * 表版本
     */
    private String tableVersion;

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 表数据长度
     */
    private Long tableDataLength;

    /**
     * 表数据行数
     */
    private Long tableDataRows;

    /**
     * 排序位置
     */
    private String tableSortPosition;

    /**
     * 表删除标识 0删除1未删除
     */
    private Long tableDeleteFlag;


    /**
     * 是否关联元模型，0-未关联，1-已关联
     */
    private String metadataModelFlag;

    /**
     * 表字段
     */
    private List<MetadataChangeColumn> metadataColumnVOList;

    /**
     * 数据源信息实体
     */
    private DatasourceDTO datasourceDTO;

}
