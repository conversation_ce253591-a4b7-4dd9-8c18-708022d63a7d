package com.dsg.database.datasource.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 元数据检核映射实体
 *
 * <AUTHOR>
 * @date 2023/6/2 16:08
 */
@Data
public class MetadataAttributeColVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *元数据类型:table、column
     */
    private String type;
    /**
     *元数据原字段实体
     */
    private MetadataChangeColumn sourceMetadataColumnVO;
    /**
     *元数据目标字段实体
     */
    private MetadataChangeColumn targetMetadataColumnVO;
    /**
     *元数据字段变更实体
     */
    private List<MetadataAttributeVO> updateColMetadata;



}
