package com.dsg.database.datasource.dto;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.utils.ParseDatasourceUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Map;

/**
 * datasource插件DTO基类
 *
 * <AUTHOR>
 * @date 2022/7/12 10:21
 */
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
public abstract class BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * datasource插件包路径
     */
    protected String dataSourcePluginPath;

    /**
     * 是否需要每次执行方法时设置datasource插件包路径，默认不需要设置
     */
    protected boolean initDataSourcePluginPath = Boolean.FALSE;

    /**
     * 数据源类型
     */
    protected String dataType;

    /**
     * 数据源类型编码
     */
    protected Integer dataTypeCode;

    /**
     * 数据源版本
     * 实际是驱动版本 24/10/09
     */
    protected String dataVersion;

    /**
     * 加密的数据源信息
     */
    protected String dataJson;

    /**
     * 数据源配置参数json
     */
    protected JSONObject dataJsonMap;

    /**
     * 在对象传递过程中需要的额外的一些参数map集合
     */
    protected Map<String, Object> extraParam;

    public JSONObject getDataJsonMap() {
        if (this.dataJsonMap == null && StringUtils.isNotBlank(this.getDataJson())) {
            this.setDataJsonMap(ParseDatasourceUtils.getDataSourceJson(this.getDataJson()));
        }
        return dataJsonMap;
    }
}
