package com.dsg.database.datasource.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.exception.ErrorCode;
import com.dsg.database.datasource.exception.RdosDefineException;
import com.dsg.database.datasource.utils.Xml2JsonUtil;
import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.dto.source.*;
import com.dtstack.dtcenter.loader.enums.RedisMode;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;


/**
 * 获取数据源对应的sourceDTO
 *
 * <AUTHOR>
 * @date 2022-07-08
 */
@Slf4j
public enum SourceDTOType {

    /**
     * clickhouse 数据源
     */
    Clickhouse(DataSourceType.Clickhouse.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            ClickHouseSourceDTO sourceDTO = ClickHouseSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.Clickhouse.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }


    },

    /**
     * Clickhouse_mrs 数据源
     */
    Clickhouse_mrs(DataSourceType.Clickhouse_mrs.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            HuaweiClickHouseSourceDTO sourceDTO = HuaweiClickHouseSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.Clickhouse_mrs.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }


    },
    /**
     * DB2数据源
     */
    DB2(DataSourceType.DB2.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            Db2SourceDTO sourceDTO = Db2SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.DB2.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }


    },
    /**
     * DB2_AS400数据源
     */
    DB2_AS400(DataSourceType.DB2_AS400.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            Db2SourceDTO sourceDTO = Db2SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.DB2_AS400.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }


    },

    /**
     * 达梦数据库
     */
    DMDB(DataSourceType.DMDB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            String schema = dataJson.containsKey(SCHEMA) ? dataJson.getString(SCHEMA) : "";
            return getSourceDTO(dataJson, confMap, schema, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            DmSourceDTO sourceDTO = DmSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.DMDB.getVal())
                    .kerberosConfig(confMap)
                    .build();
            if (StringUtils.isNotEmpty(schema)) {
                sourceDTO.setSchema(schema);
            }
            return sourceDTO;
        }


    },


    /**
     * CarbonData
     */
    CarbonData(DataSourceType.CarbonData.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String defaultFS = null;
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            HiveSourceDTO hiveSourceDTO = HiveSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.HIVE.getVal())
                    .defaultFS(defaultFS)
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();
            return hiveSourceDTO;
        }
    },

    /**
     * ftp
     */
    FTP(DataSourceType.FTP.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String host = dataJson.containsKey("host") ? dataJson.getString("host") : "";
            String port = dataJson.containsKey("port") ? dataJson.getString("port") : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String protocol = dataJson.containsKey("protocol") ? dataJson.getString("protocol") : "";
            String connectMode = dataJson.containsKey("connectMode") ? dataJson.getString("connectMode") : "";
            String auth = dataJson.containsKey("auth") ? dataJson.getString("auth") : "";
            String rasPath = dataJson.containsKey("rasPath") ? dataJson.getString("rasPath") : "";
            FtpSourceDTO sourceDTO = FtpSourceDTO
                    .builder()
                    .url(host)
                    .hostPort(port)
                    .username(username)
                    .password(password)
                    .protocol(protocol)
                    .connectMode(connectMode)
                    .auth(auth)
                    .path(rasPath)
                    .sourceType(DataSourceType.FTP.getVal())
                    .build();
            return sourceDTO;
        }
    },


    /**
     * sftp
     */
    SFTP(DataSourceType.SFTP.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String host = dataJson.containsKey("host") ? dataJson.getString("host") : "";
            String port = dataJson.containsKey("port") ? dataJson.getString("port") : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String protocol = dataJson.containsKey("protocol") ? dataJson.getString("protocol") : "";
            String connectMode = dataJson.containsKey("connectMode") ? dataJson.getString("connectMode") : "";
            String auth = dataJson.containsKey("auth") ? dataJson.getString("auth") : "";
            String rasPath = dataJson.containsKey("rasPath") ? dataJson.getString("rasPath") : "";
            FtpSourceDTO sourceDTO = FtpSourceDTO
                    .builder()
                    .url(host)
                    .hostPort(port)
                    .username(username)
                    .password(password)
                    .protocol(protocol)
                    .connectMode(connectMode)
                    .auth(auth)
                    .path(rasPath)
                    .sourceType(DataSourceType.SFTP.getVal())
                    .build();
            return sourceDTO;
        }
    },

    /**
     * SFTP
     */
    HOST(DataSourceType.HOST.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String host = dataJson.containsKey("host") ? dataJson.getString("host") : "";
            String port = dataJson.containsKey("port") ? dataJson.getString("port") : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String protocol = dataJson.containsKey("protocol") ? dataJson.getString("protocol") : "";
            String auth = dataJson.containsKey("auth") ? dataJson.getString("auth") : "";
            String rasPath = "";
            if (dataJson.containsKey("uploadFilePath")) {
                JSONObject dataObj = dataJson.getJSONObject("uploadFilePath");
                if (dataObj.containsKey("rsaPath")) {
                    rasPath = dataObj.getString("rsaPath");
                }
            }
            FtpSourceDTO sourceDTO = FtpSourceDTO
                    .builder()
                    .url(host)
                    .hostPort(port)
                    .username(username)
                    .password(password)
                    .protocol(protocol)
                    .auth(auth)
                    .path(rasPath)
                    .sourceType(DataSourceType.FTP.getVal())
                    .build();
            return sourceDTO;
        }
    },

    /**
     * gbase
     */
    GBase_8a(DataSourceType.GBase_8a.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            GBaseSourceDTO sourceDTO = GBaseSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.GBase_8a.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * greenplum
     */
    GREENPLUM6(DataSourceType.GREENPLUM6.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            String schema = dataJson.containsKey(SCHEMA) ? dataJson.getString(SCHEMA) : "";
            return getSourceDTO(dataJson, confMap, schema, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            Greenplum6SourceDTO sourceDTO = Greenplum6SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.GREENPLUM6.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * es
     */
    ES(DataSourceType.ES.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String address = dataJson.containsKey("address") ? dataJson.getString("address") : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            ESSourceDTO esSourceDTO = ESSourceDTO
                    .builder()
                    .url(address)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.ES.getVal())
                    .build();
            return esSourceDTO;
        }
    },

    /**
     * es6
     */
    ES6(DataSourceType.ES6.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String address = dataJson.containsKey("address") ? dataJson.getString("address") : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            ESSourceDTO esSourceDTO = ESSourceDTO
                    .builder()
                    .url(address)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.ES6.getVal())
                    .build();
            return esSourceDTO;
        }
    },

    /**
     * es7
     */
    ES7(DataSourceType.ES7.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String address = dataJson.containsKey("address") ? dataJson.getString("address") : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            ES7SourceDTO esSourceDTO = ES7SourceDTO
                    .builder()
                    .url(address)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .keyPath((String) expandConfig.get(SSL_LOCAL_DIR))
                    .sourceType(DataSourceType.ES7.getVal())
                    .build();
            return esSourceDTO;
        }
    },

    /**
     * es8
     */
    ES8(DataSourceType.ES8.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String address = dataJson.containsKey("address") ? dataJson.getString("address") : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            ES8SourceDTO esSourceDTO = ES8SourceDTO
                    .builder()
                    .url(address)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .keyPath((String) expandConfig.get(SSL_LOCAL_DIR))
                    .sourceType(DataSourceType.ES8.getVal())
                    .build();
            return esSourceDTO;
        }
    },

    /**
     * hbase
     */
    HBASE(DataSourceType.HBASE.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (dataJson.containsKey(HBASECONFIG)) {
                String hbaseConfig = dataJson.getString(HBASECONFIG);
                HbaseSourceDTO hbaseSourceDTO = HbaseSourceDTO
                        .builder()
                        .config(hbaseConfig)
                        .sourceType(DataSourceType.HBASE.getVal())
                        .kerberosConfig(confMap)
                        .build();
                return hbaseSourceDTO;
            }


            String hbaseQuorum = dataJson.containsKey("hbase_quorum") ? dataJson.getString("hbase_quorum") : "";
            String hbaseParent = dataJson.containsKey("hbase_parent") ? dataJson.getString("hbase_parent") : "";
            String hbaseOther = dataJson.containsKey("hbase_other") ? dataJson.getString("hbase_other") : "";
            HbaseSourceDTO hbaseSourceDTO = HbaseSourceDTO
                    .builder()
                    .url(hbaseQuorum)
                    .path(hbaseParent)
                    .schema(schema)
                    .sourceType(DataSourceType.HBASE.getVal())
                    .others(hbaseOther)
                    .kerberosConfig(confMap)
                    .build();
            return hbaseSourceDTO;

        }

    },

    /**
     * hbase
     */
    HBASE2(DataSourceType.HBASE2.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String hbaseQuorum = dataJson.containsKey("hbase_quorum") ? dataJson.getString("hbase_quorum") : "";
            String hbaseParent = dataJson.containsKey("hbase_parent") ? dataJson.getString("hbase_parent") : "";
            String hbaseOther = dataJson.containsKey("hbase_other") ? dataJson.getString("hbase_other") : "";
            String nameSpace = dataJson.containsKey("nameSpace") ? dataJson.getString("nameSpace") : "";
            HbaseSourceDTO hbaseSourceDTO = HbaseSourceDTO
                    .builder()
                    .url(hbaseQuorum)
                    .path(hbaseParent)
                    .schema(nameSpace)
                    .sourceType(DataSourceType.HBASE.getVal())
                    .others(hbaseOther)
                    .kerberosConfig(confMap)
                    .build();
            return hbaseSourceDTO;
        }
    },


    /**
     * huawei_hbase
     */
    HBASE_MRS(DataSourceType.HBASE_MRS.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String hbaseQuorum = dataJson.containsKey("hbase_quorum") ? dataJson.getString("hbase_quorum") : "";
            String hbaseParent = dataJson.containsKey("hbase_parent") ? dataJson.getString("hbase_parent") : "";
            String hbaseOther = dataJson.containsKey("hbase_other") ? dataJson.getString("hbase_other") : "";
            String nameSpace = dataJson.containsKey("nameSpace") ? dataJson.getString("nameSpace") : "";
            //获取认证用户
            String authUser = dataJson.containsKey("authUser") ? dataJson.getString("authUser") : "";
            log.warn("519 hbase_mrs confMap参数{}", confMap);
            Map<String, Object> xmlMap = new HashMap<>();
            if(confMap!=null && !confMap.isEmpty()){
                if (confMap.containsKey(HadoopConfig.CORE_SITE.getVal())) {
                    Map<String, Object> xmlMap1 = new HashMap<>();
                    xmlMap1 = Xml2JsonUtil.getXmlMap(confMap.get(HadoopConfig.CORE_SITE.getVal()).toString(),new HashMap<>());
                    if(xmlMap1!=null && !xmlMap1.isEmpty()){
                        xmlMap.putAll(xmlMap1);
                    }
                }
                if (confMap.containsKey(HadoopConfig.HDFS_SITE.getVal())) {
                    Map<String, Object> xmlMap1 = new HashMap<>();
                    xmlMap1 = Xml2JsonUtil.getXmlMap(confMap.get(HadoopConfig.HDFS_SITE.getVal()).toString(),new HashMap<>());
                    if(xmlMap1!=null && !xmlMap1.isEmpty()){
                        xmlMap.putAll(xmlMap1);
                    }
                }

                if (confMap.containsKey(HadoopConfig.HBASE_SITE.getVal())) {
                    Map<String, Object> xmlMap1 = new HashMap<>();
                    xmlMap1 = Xml2JsonUtil.getXmlMap(confMap.get(HadoopConfig.HBASE_SITE.getVal()).toString(),new HashMap<>());
                    if(xmlMap1!=null && !xmlMap1.isEmpty()){
                        xmlMap.putAll(xmlMap1);
                    }
                }
            }

            HuaweiHbaseSourceDTO hbaseSourceDTO = HuaweiHbaseSourceDTO
                    .builder()
                    .url(hbaseQuorum)
                    .path(hbaseParent)
                    .schema(nameSpace)
                    .sourceType(DataSourceType.HBASE_MRS.getVal())
                    .others(hbaseOther)
                    .kerberosConfig(confMap)
                    .config(JSONObject.toJSONString(xmlMap))
                    .authUser(authUser)
                    .build();
            return hbaseSourceDTO;
        }
    },


    /**
     * kafka 0.10
     */
    KAFKA_10(DataSourceType.KAFKA_10.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            return buildKafkaSourceDTO(dataJson, confMap);
        }
    },

    /**
     * kafka 0.11
     */
    KAFKA_11(DataSourceType.KAFKA_11.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            return buildKafkaSourceDTO(dataJson, confMap);
        }
    },

    /**
     * kafka 1.x
     */
    KAFKA(DataSourceType.KAFKA.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            return buildKafkaSourceDTO(dataJson, confMap);
        }
    },


    /**
     * kafka 2.x
     */
    KAFKA_2X(DataSourceType.KAFKA_2X.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            return buildKafkaSourceDTO(dataJson, confMap);
        }
    },
    /**
     * kafka 2.x
     */
    HIGH_VERSION_KAFKA(DataSourceType.HIGH_VERSION_KAFKA.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            return buildKafkaSourceDTO(dataJson, confMap);
        }
    },


    /**
     * SparkThrift2_1
     */
    SparkThrift2_1(DataSourceType.SparkThrift2_1.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String defaultFS = null;
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                    throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                }
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            SparkSourceDTO sparkSourceDTO = SparkSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.HIVE.getVal())
                    .defaultFS(defaultFS)
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();
            return sparkSourceDTO;
        }
    },

    /**
     * hdfs
     */
    HDFS(DataSourceType.HDFS.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
            if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
            }
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal())) {
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            String remoteUser = null;
            if (dataJson.containsKey(HadoopConfig.HDFS_REMOTE_USER.getVal())) {
                remoteUser = dataJson.getString(HadoopConfig.HDFS_REMOTE_USER.getVal());
                if (null == confMap) {
                    confMap = new HashMap<>();
                }
                confMap.put(HadoopConfig.HDFS_REMOTE_USER.getVal(), remoteUser);
            }
            HdfsSourceDTO hdfsSourceDTO = HdfsSourceDTO
                    .builder()
                    .defaultFS(defaultFS)
                    .schema(schema)
                    .sourceType(DataSourceType.HDFS.getVal())
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();

            return hdfsSourceDTO;
        }
    },

    /**
     * huawei_hdfs
     */
    HDFS_MRS(DataSourceType.HDFS_MRS.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            //获取认证用户
            String authUser = dataJson.containsKey("authUser") ? dataJson.getString("authUser") : "";
            String openKerberos = dataJson.containsKey("openKerberos") ? dataJson.getString("openKerberos") : "close";
            String defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
            if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
            }
            String hadoopConfig = null;
            log.error("717 hdfs_mrs confMap参数{}", confMap);
            log.error("===========HDFS_MRS======xmlMap3开始读取文件");
            Map<String, Object> xmlMap = new HashMap<>();
            try{
                if (confMap.containsKey(HadoopConfig.CORE_SITE.getVal())){
                    Map<String, Object> xmlMap1=new HashMap<>();
                    xmlMap1 = Xml2JsonUtil.getXmlMap(confMap.get(HadoopConfig.CORE_SITE.getVal()).toString(),new HashMap<>());
                    if(xmlMap1!=null && !xmlMap1.isEmpty() ){
                        xmlMap.putAll(xmlMap1);
                    }
                    boolean b = xmlMap.containsKey("hadoop.rpc.protection");
                    log.error("============hdfs_mrs_CORE_SITE是否含有[hadoop.rpc.protection]:{},值是{}",b,xmlMap.get("hadoop.rpc.protection"));
                }
                log.error("===========HDFS_MRS======xmlMap1,{}",xmlMap);
                if (confMap.containsKey(HadoopConfig.HDFS_SITE.getVal())) {
                    Map<String, Object> xmlMap1=new HashMap<>();
                    xmlMap1 = Xml2JsonUtil.getXmlMap(confMap.get(HadoopConfig.HDFS_SITE.getVal()).toString(),new HashMap<>());
                    if(xmlMap1!=null && !xmlMap1.isEmpty() ){
                        xmlMap.putAll(xmlMap1);
                    }
                }
                log.error("===========HDFS_MRS======xmlMap2,{}",xmlMap);
                if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                    defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                    if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                        throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                    }
                    hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
                    if (StringUtils.isNotEmpty(hadoopConfig)) {
                        JSONObject jsonObject = JSONObject.parseObject(hadoopConfig);
                        xmlMap.putAll(jsonObject);
                    }
                }
            }catch (Exception e){
                log.error("===========HDFS_MRS======xmlMap3结束读取文件异常：{}",e.getMessage());
            }

            log.error("===========HDFS_MRS======xmlMap3,{}",xmlMap);
            boolean b = xmlMap.containsKey("hadoop.rpc.protection");
            log.error("============hdfs_mrs_getSourceDTO是否含有[hadoop.rpc.protection]:{},值是{}",b,xmlMap.get("hadoop.rpc.protection"));
            log.error("============hdfs_mrs_getSourceDTO是否含有[xmlMap]:{},值是{}",xmlMap,xmlMap.size());
            log.error("===========HDFS_MRS======xmlMap3结束读取文件");
            HuaweiHdfsSourceDTO hdfsSourceDTO = HuaweiHdfsSourceDTO
                    .builder()
                    .defaultFS(defaultFS)
                    .schema(schema)
                    .sourceType(DataSourceType.HDFS_MRS.getVal())
                    .config(JSONObject.toJSONString(xmlMap))
                    .kerberosConfig(confMap)
                    .authUser(authUser)
                    .openKerberos(openKerberos)
                    .build();

            return hdfsSourceDTO;
        }
    },

    /**
     * hdfs3.x版本
     */
    HDFS3(DataSourceType.HDFS3.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
            if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
            }
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal())) {
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }

            HdfsSourceDTO hdfsSourceDTO = HdfsSourceDTO
                    .builder()
                    .defaultFS(defaultFS)
                    .schema(schema)
                    .sourceType(DataSourceType.HDFS3.getVal())
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();

            return hdfsSourceDTO;
        }
    },
    /**
     * hive
     */
    HIVE3(DataSourceType.HIVE3X.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String defaultFS = null;
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                    throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                }
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            Hive3SourceDTO hiveSourceDTO = Hive3SourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.HIVE3X.getVal())
                    .defaultFS(defaultFS)
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();
            return hiveSourceDTO;
        }
    },

    /**
     * hive
     */
    HIVE3X_MRS(DataSourceType.HIVE3X_MRS.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            //获取认证用户
            String authUser = dataJson.containsKey("authUser") ? dataJson.getString("authUser") : "";
            String defaultFS = null;
            String hadoopConfig = null;
            log.warn("735hive3x_mrs confMap参数{}", confMap);
            Map<String, Object> xmlMap = new HashMap<>();
            if(confMap!=null && !confMap.isEmpty()){
                if (confMap.containsKey(HadoopConfig.CORE_SITE.getVal())) {
                    xmlMap = Xml2JsonUtil.getXmlMap(confMap.get(HadoopConfig.CORE_SITE.getVal()).toString(),xmlMap);
                }
                if (confMap.containsKey(HadoopConfig.HDFS_SITE.getVal())) {
                    xmlMap = Xml2JsonUtil.getXmlMap(confMap.get(HadoopConfig.HDFS_SITE.getVal()).toString(),xmlMap);
                }
                if (confMap.containsKey(HadoopConfig.HIVE_SITE.getVal())) {
                    xmlMap = Xml2JsonUtil.getXmlMap(confMap.get(HadoopConfig.HIVE_SITE.getVal()).toString(),xmlMap);
                }
                if (confMap.containsKey(HadoopConfig.HIVE_CLIENT.getVal())) {
                    xmlMap = Xml2JsonUtil.getXmlMap(confMap.get(HadoopConfig.HIVE_CLIENT.getVal()).toString(),xmlMap);
                }
            }
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                    throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                }
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
                if (StringUtils.isNotEmpty(hadoopConfig)) {
                    JSONObject jsonObject = JSONObject.parseObject(hadoopConfig);
                    xmlMap.putAll(jsonObject);
                }
            }
            HuaweiHive3SourceDTO hiveSourceDTO = HuaweiHive3SourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.HIVE3X_MRS.getVal())
                    .defaultFS(defaultFS)
                    .config(JSONObject.toJSONString(xmlMap))
                    .kerberosConfig(confMap)
                    .authUser(authUser)
                    .build();
            return hiveSourceDTO;
        }
    },

    /**
     * hive
     */
    HIVE(DataSourceType.HIVE.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String defaultFS = null;
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                    throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                }
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            HiveSourceDTO hiveSourceDTO = HiveSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.HIVE.getVal())
                    .defaultFS(defaultFS)
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();
            return hiveSourceDTO;
        }
    },

    /**
     * hive 1.x
     */
    HIVE1X(DataSourceType.HIVE1X.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String defaultFS = null;
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                    throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                }
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            Hive1SourceDTO hiveSourceDTO = Hive1SourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.HIVE1X.getVal())
                    .defaultFS(defaultFS)
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();
            return hiveSourceDTO;
        }
    },

    /**
     * impala
     */
    IMPALA(DataSourceType.IMPALA.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String defaultFS = null;
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                    throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                }
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            ImpalaSourceDTO sourceDTO = ImpalaSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.IMPALA.getVal())
                    .defaultFS(defaultFS)
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * kudu
     */
    Kudu(DataSourceType.Kudu.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (null == dataJson || StringUtils.isBlank(dataJson.getString("hostPorts"))) {
                return KuduSourceDTO.builder().build();
            }
            KuduSourceDTO kuduSourceDTO = KuduSourceDTO
                    .builder()
                    .url(dataJson.getString("hostPorts"))
                    .schema(schema)
                    .sourceType(DataSourceType.Kudu.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return kuduSourceDTO;
        }
    },

    /**
     * AWSS3
     */
    AWSS3(DataSourceType.AWS_S3.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String accessKey = dataJson.containsKey("accessKey") ? dataJson.getString("accessKey") : "";
            String region = dataJson.containsKey("region") ? dataJson.getString("region") : "";
            String secretKey = dataJson.containsKey("secretKey") ? dataJson.getString("secretKey") : "";
            String endPoint = dataJson.containsKey("endPoint") ? dataJson.getString("endPoint") : "";
            AwsS3SourceDTO awsS3SourceDTO = AwsS3SourceDTO
                    .builder()
                    .accessKey(accessKey)
                    .region(region)
                    .secretKey(secretKey)
                    .endPoint(endPoint)
                    .build();
            return awsS3SourceDTO;
        }
    },

    /**
     * INSPUR_S3
     */
    INSPUR_S3(DataSourceType.INSPUR_S3.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String accessKey = dataJson.containsKey("accessKey") ? dataJson.getString("accessKey") : "";
            String region = dataJson.containsKey("region") ? dataJson.getString("region") : "";
            String secretKey = dataJson.containsKey("secretKey") ? dataJson.getString("secretKey") : "";
            String endPoint = dataJson.containsKey("endPoint") ? dataJson.getString("endPoint") : "";
            InspurS3SourceDTO inspurS3SourceDTO = InspurS3SourceDTO
                    .builder()
                    .accessKey(accessKey)
                    .region(region)
                    .secretKey(secretKey)
                    .endPoint(endPoint)
                    .build();
            return inspurS3SourceDTO;
        }
    },

    /**
     * AWSS3
     */
    OSS_ALI(DataSourceType.OSS_ALI.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String accessKey = dataJson.containsKey("accessKey") ? dataJson.getString("accessKey") : "";
            String secretKey = dataJson.containsKey("secretKey") ? dataJson.getString("secretKey") : "";
            String endPoint = dataJson.containsKey("endPoint") ? dataJson.getString("endPoint") : "";
            String bucket = dataJson.containsKey("Bucket") ? dataJson.getString("Bucket") : "";
            OssSourceDTO ossSourceDTO = OssSourceDTO
                    .builder()
                    .accessKey(accessKey)
                    .secretKey(secretKey)
                    .endPoint(endPoint)
                    .bucket(bucket)
                    .build();
            return ossSourceDTO;
        }
    },


    /**
     * DATA_HUB
     */
    DATA_HUB(DataSourceType.DATA_HUB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String accessId = dataJson.containsKey("accessId") ? dataJson.getString("accessId") : "";
            String accessKey = dataJson.containsKey("accessKey") ? dataJson.getString("accessKey") : "";
            String endPoint = dataJson.containsKey("endPoint") ? dataJson.getString("endPoint") : "";
            String projectName = dataJson.containsKey("projectName") ? dataJson.getString("projectName") : "";
            DatahubSourceDTO datahubSourceDTO = DatahubSourceDTO
                    .builder()
                    .accessKey(accessKey)
                    .accessId(accessId)
                    .endPoint(endPoint)
                    .projectName(projectName)
                    .build();
            return datahubSourceDTO;
        }
    },

    /**
     * OSS_HUAWEI
     */
    OSS_HUAWEI(DataSourceType.OSS_HUAWEI.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String accessKey = dataJson.containsKey("accessKey") ? dataJson.getString("accessKey") : "";
            String secretKey = dataJson.containsKey("secretKey") ? dataJson.getString("secretKey") : "";
            String endPoint = dataJson.containsKey("endPoint") ? dataJson.getString("endPoint") : "";
            OssHuaweiSourceDTO ossSourceDTO = OssHuaweiSourceDTO
                    .builder()
                    .accessKey(accessKey)
                    .secretKey(secretKey)
                    .endPoint(endPoint)
                    .build();
            return ossSourceDTO;
        }
    },

    /**
     * OSS_LC
     */
    OSS_LC(DataSourceType.OSS_LC.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String accessKey = dataJson.containsKey("accessKey") ? dataJson.getString("accessKey") : "";
            String secretKey = dataJson.containsKey("secretKey") ? dataJson.getString("secretKey") : "";
            String endPoint = dataJson.containsKey("endPoint") ? dataJson.getString("endPoint") : "";
            OssLcSourceDTO ossSourceDTO = OssLcSourceDTO
                    .builder()
                    .accessKey(accessKey)
                    .secretKey(secretKey)
                    .endPoint(endPoint)
                    .build();
            return ossSourceDTO;
        }
    },

    S3(DataSourceType.S3.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String username = dataJson.containsKey("username") ? dataJson.getString("username") : "";
            String password = dataJson.containsKey("password") ? dataJson.getString("password") : "";
            String hostname = dataJson.containsKey("hostname") ? dataJson.getString("hostname") : "";
            S3SourceDTO s3SourceDTO = S3SourceDTO
                    .builder()
                    .username(username)
                    .password(password)
                    .hostname(hostname)
                    .build();
            return s3SourceDTO;
        }
    },

    /**
     * kylin
     */
    Kylin(DataSourceType.Kylin.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            KylinSourceDTO sourceDTO = KylinSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Kylin.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * libra
     */
    LIBRA(DataSourceType.LIBRA.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            LibraSourceDTO sourceDTO = LibraSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.LIBRA.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * mongodb
     */
    MONGODB(DataSourceType.MONGODB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("database");
            }
            String hostPorts = dataJson.containsKey("hostPorts") ? dataJson.getString("hostPorts") : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            MongoSourceDTO mongoSourceDTO = MongoSourceDTO
                    .builder()
                    .hostPort(hostPorts)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.MONGODB.getVal())
                    .schema(schema)
                    .build();
            return mongoSourceDTO;
        }
    },

    /**
     * mysql
     */
    MySQL(DataSourceType.MySQL.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            Mysql5SourceDTO sourceDTO = Mysql5SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.MySQL.getVal())
                    .kerberosConfig(confMap)
//                    .poolConfig(PoolConfig.builder().build())
                    .build();
            return sourceDTO;
        }
    },


    /**
     * mysql
     */
    DWS_MySQL(DataSourceType.DWS_MySQL.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            DwsMysqlSourceDTO sourceDTO = DwsMysqlSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.DWS_MySQL.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },


    /**
     * ADS
     */
    ADS(DataSourceType.ADS.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            AdsForMysqlSourceDTO sourceDTO = AdsForMysqlSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.ADS.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * sequoiadb_for_mysql
     */
    Sequoiadb_FOR_MYSQL(DataSourceType.Sequoiadb_FOR_MYSQL.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            SequoiadbForMysqlSourceDTO sourceDTO = SequoiadbForMysqlSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Sequoiadb_FOR_MYSQL.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * StarRocks
     */
    StarRocks(DataSourceType.StarRocks.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            StarRocksSourceDTO sourceDTO = StarRocksSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.StarRocks.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * doris
     */
    Doris0(DataSourceType.DORIS.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            DorisSourceDTO sourceDTO = DorisSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.DORIS.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * doris
     */
    Doris1(DataSourceType.DORIS1.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            DorisSourceDTO sourceDTO = DorisSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.DORIS1.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },


    /**
     * doris
     */
    Doris2(DataSourceType.DORIS2.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            DorisSourceDTO sourceDTO = DorisSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.DORIS2.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * tidb
     */
    TiDB(DataSourceType.TiDB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            TiDbSourceDTO sourceDTO = TiDbSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.TiDB.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * goldenDb
     */
    GoldenDB(DataSourceType.GoldenDB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            GoldenDbSourceDTO sourceDTO = GoldenDbSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.GoldenDB.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * mysql8
     */
    MySQL8(DataSourceType.MySQL8.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            Mysql8SourceDTO sourceDTO = Mysql8SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.MySQL8.getVal())
                    .kerberosConfig(confMap)
//                    .poolConfig(PoolConfig.builder().build())
                    .build();
            return sourceDTO;
        }
    },

    /**
     * MySQL5_HIVE
     */
    MySQL5_HIVE(DataSourceType.MySQL5_HIVE.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            Mysql5HiveSourceDTO sourceDTO = Mysql5HiveSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.MySQL5_HIVE.getVal())
                    .kerberosConfig(confMap)
//                    .poolConfig(PoolConfig.builder().build())
                    .build();
            return sourceDTO;
        }
    },

    /**
     * MySQL8_HIVE
     */
    MySQL8_HIVE(DataSourceType.MySQL8_HIVE.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            Mysql8HiveSourceDTO sourceDTO = Mysql8HiveSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.MySQL8_HIVE.getVal())
                    .kerberosConfig(confMap)
//                    .poolConfig(PoolConfig.builder().build())
                    .build();
            return sourceDTO;
        }
    },

    /**
     * GaussDB_FOR_MySQL8
     */
    GaussDB_FOR_MySQL(DataSourceType.GaussDB_FOR_MySQL.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            GaussForMysql8SourceDTO sourceDTO = GaussForMysql8SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.GaussDB_FOR_MySQL.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },
    /**
     * MariaDB
     */
    MariaDB(DataSourceType.MariaDB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            MariaDBSourceDTO sourceDTO = MariaDBSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.MariaDB.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * TDSQL_FOR_MySQL
     */
    TDSQL_FOR_MySQL(DataSourceType.TDSQL_FOR_MySQL.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            Mysql8SourceDTO sourceDTO = Mysql8SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.TDSQL_FOR_MySQL.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },


    /**
     * TDSQL_FOR_PG
     */
    TDSQL_FOR_PG(DataSourceType.TDSQL_FOR_PG.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            PostgresqlSourceDTO sourceDTO = PostgresqlSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.TDSQL_FOR_PG.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * TDSQL_FOR_ORACLE
     */
    TDSQL_FOR_ORACLE(DataSourceType.TDSQL_FOR_ORACLE.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            PostgresqlSourceDTO sourceDTO = PostgresqlSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.TDSQL_FOR_ORACLE.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * maxcompute
     */
    MAXCOMPUTE(DataSourceType.MAXCOMPUTE.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            Map<String, String> properties = JSONObject.parseObject(dataJson.toString(), Map.class);
            return OdpsSourceDTO
                    .builder()
                    .config(JSON.toJSONString(properties))
                    .sourceType(DataSourceType.MAXCOMPUTE.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
        }
    },

    /**
     * phoennix
     */
    Phoenix(DataSourceType.Phoenix.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            PhoenixSourceDTO sourceDTO = PhoenixSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Phoenix.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * phoennix5
     */
    Phoenix5X(DataSourceType.PHOENIX5.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            Phoenix5SourceDTO sourceDTO = Phoenix5SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.PHOENIX5.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * postgresql
     */
    PostgreSQL(DataSourceType.PostgreSQL.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            PostgresqlSourceDTO sourceDTO = PostgresqlSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.PostgreSQL.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * postgresql
     */
    TBase(DataSourceType.TBase.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            PostgresqlSourceDTO sourceDTO = PostgresqlSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.TBase.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },
    /**
     * DWS_PG
     */
    DWS_PG(DataSourceType.DWS_PG.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            DwsPostgresqlSourceDTO sourceDTO = DwsPostgresqlSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.DWS_PG.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },
    /**
     * redis
     */
    REDIS(DataSourceType.REDIS.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("database");
            }
            String password = dataJson.getString(JDBC_PASSWORD);
            Integer redisType = dataJson.getInteger("redisType");
            RedisMode redisMode = null;
            if (redisType != null) {
                redisMode = RedisMode.getRedisModel(redisType);
            }
            String masterName = dataJson.getString("masterName");
            String hostPort = dataJson.getString("hostPort");
            RedisSourceDTO redisSourceDTO = RedisSourceDTO
                    .builder()
                    .password(password)
                    .sourceType(DataSourceType.REDIS.getVal())
                    .redisMode(redisMode)
                    .master(masterName)
                    .schema(schema)
                    .hostPort(hostPort).build();
            return redisSourceDTO;
        }
    },

    /**
     * sqlserver 2017
     */
    SQLSERVER_2017_LATER(DataSourceType.SQLSERVER_2017_LATER.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.getString(JDBC_URL);
            String username = dataJson.getString(JDBC_USERNAME);
            String password = dataJson.getString(JDBC_PASSWORD);
            Sqlserver2017SourceDTO sourceDTO = Sqlserver2017SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.SQLServer.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /*
     * sqlserver
     */
    SQLServer(DataSourceType.SQLServer.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            SqlserverSourceDTO sourceDTO = SqlserverSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.SQLServer.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    OceanBase_FOR_MYSQL(DataSourceType.OceanBase.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            OceanBaseSourceDTO oceanBaseSourceDTO = OceanBaseSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.OceanBase.getVal())
                    .schema(schema)
                    .build();
            return oceanBaseSourceDTO;
        }
    },

    OceanBase_FOR_ORACLE(DataSourceType.OceanBase_FOR_ORACLE.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            OceanBaseSourceDTO oceanBaseSourceDTO = OceanBaseSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.OceanBase_FOR_ORACLE.getVal())
                    .schema(schema)
                    .build();
            return oceanBaseSourceDTO;
        }
    },

    /**
     * oracle
     */
    Oracle(DataSourceType.Oracle.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String filePath = "";
            if (dataJson.containsKey("uploadFilePath")) {
                JSONObject dataObj = dataJson.getJSONObject("uploadFilePath");
                if (dataObj.containsKey("filePath")) {
                    filePath = dataObj.getString("filePath");
                }
            }

            OracleSourceDTO oracleSourceDTO = OracleSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Oracle.getVal())
                    .schema(schema)
                    .tnsOraPath(filePath)
//                    .poolConfig(PoolConfig.builder().build())
                    .build();
            return oracleSourceDTO;
        }
    },

    /**
     * oracle_9i
     */
    Oracle_9i(DataSourceType.Oracle_9i.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String filePath = "";
            if (dataJson.containsKey("uploadFilePath")) {
                JSONObject dataObj = dataJson.getJSONObject("uploadFilePath");
                if (dataObj.containsKey("filePath")) {
                    filePath = dataObj.getString("filePath");
                }
            }

            OracleSourceDTO oracleSourceDTO = OracleSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Oracle.getVal())
                    .schema(schema)
                    .tnsOraPath(filePath)
//                    .poolConfig(PoolConfig.builder().build())
                    .build();
            return oracleSourceDTO;
        }
    },

    /**
     * oracle_19c
     */
    Oracle_19c(DataSourceType.Oracle_19c.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String filePath = "";
            if (dataJson.containsKey("uploadFilePath")) {
                JSONObject dataObj = dataJson.getJSONObject("uploadFilePath");
                if (dataObj.containsKey("filePath")) {
                    filePath = dataObj.getString("filePath");
                }
            }

            OracleSourceDTO oracleSourceDTO = OracleSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Oracle_19c.getVal())
                    .schema(schema)
                    .tnsOraPath(filePath)
//                    .poolConfig(PoolConfig.builder().build())
                    .build();
            return oracleSourceDTO;
        }
    },

    /**
     * Informix
     */
    Informix(DataSourceType.Informix.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            InformixSourceDTO informixSourceDTO = InformixSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Informix.getVal())
                    .schema(username)
                    .build();
            return informixSourceDTO;
        }
    },

    /**
     * kingbaseES
     */
    KINGBASE8(DataSourceType.KINGBASE8.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            KingbaseSourceDTO kingbaseSourceDTO = KingbaseSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.KINGBASE8.getVal())
                    .build();
            return kingbaseSourceDTO;
        }
    },

    /**
     * Inceptor
     */
    Inceptor(DataSourceType.INCEPTOR.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String hiveMetastoreUris = dataJson.containsKey("hive.metastore.uris") ? dataJson.getString("hive.metastore.uris") : "";
            String defaultFS = null;
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                    throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                }
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            InceptorSourceDTO hiveSourceDTO = InceptorSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .metaStoreUris(hiveMetastoreUris)
                    .sourceType(DataSourceType.INCEPTOR.getVal())
                    .defaultFS(defaultFS)
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();
            return hiveSourceDTO;
        }
    },


    /**
     * Inceptor8
     */
    INCEPTOR8(DataSourceType.INCEPTOR8.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String hiveMetastoreUris = dataJson.containsKey("hive.metastore.uris") ? dataJson.getString("hive.metastore.uris") : "";
            String defaultFS = null;
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                    throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                }
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            InceptorSourceDTO hiveSourceDTO = InceptorSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .metaStoreUris(hiveMetastoreUris)
                    .sourceType(DataSourceType.INCEPTOR8.getVal())
                    .defaultFS(defaultFS)
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();
            return hiveSourceDTO;
        }
    },

    /**
     * ArgoDB
     */
    ArgoDB(DataSourceType.ArgoDB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String hiveMetastoreUris = dataJson.containsKey("hive.metastore.uris") ? dataJson.getString("hive.metastore.uris") : "";
            String defaultFS = null;
            String hadoopConfig = null;
            if (dataJson.containsKey(HadoopConfig.HADOOP_CONFIG.getVal()) && StringUtils.isNotBlank(dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal()))) {
                defaultFS = dataJson.getString(HadoopConfig.HDFS_DEFAULTFS.getVal());
                if (!defaultFS.matches(HadoopConfig.DEFAULT_FS_REGEX.getVal())) {
                    throw new RdosDefineException(ErrorCode.ERROR_DEFAULT_FS_FORMAT);
                }
                hadoopConfig = dataJson.getString(HadoopConfig.HADOOP_CONFIG.getVal());
            }
            ArgodbSourceDTO argodbSourceDTO = ArgodbSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .metaStoreUris(hiveMetastoreUris)
                    .sourceType(DataSourceType.ArgoDB.getVal())
                    .defaultFS(defaultFS)
                    .config(hadoopConfig)
                    .kerberosConfig(confMap)
                    .build();
            return argodbSourceDTO;
        }
    },

    /**
     * InfluxDB
     */
    InfluxDB(DataSourceType.INFLUXDB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String url = dataJson.containsKey("url") ? dataJson.getString("url") : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            String retentionPolicy = dataJson.containsKey("retentionPolicy") ? dataJson.getString("retentionPolicy") : "";
            InfluxDBSourceDTO influxDBSourceDTO = InfluxDBSourceDTO
                    .builder()
                    .url(url)
                    .username(username)
                    .password(password)
                    .database(schema)
                    .retentionPolicy(retentionPolicy)
                    .build();
            return influxDBSourceDTO;
        }
    },

    /**
     * AnalyticDB PostgreSQL
     */
    ADB_FOR_PG(DataSourceType.ADB_FOR_PG.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            AdbForPgSourceDTO sourceDTO = AdbForPgSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.ADB_FOR_PG.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * gp PostgreSQL
     */
    GP_FOR_PG(DataSourceType.GP_FOR_PG.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            GpForPgSourceDTO sourceDTO = GpForPgSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.GP_FOR_PG.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * KunDB
     */
    KunDB(DataSourceType.KunDB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            KunDBSourceDTO sourceDTO = KunDBSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.KunDB.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },


    /**
     * GaussDB
     */

    GaussDB(DataSourceType.GaussDB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            GaussDBSourceDTO sourceDTO = GaussDBSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.GaussDB.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },

    /**
     * GaussDB
     */

    GaussDB_HIVE(DataSourceType.GaussDB_HIVE.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            GaussDBHiveSourceDTO sourceDTO = GaussDBHiveSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.GaussDB_HIVE.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },
    /**
     * GaussDB200
     */

    Gauss_DB200(DataSourceType.Gauss_DB200.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            if (StringUtils.isBlank(schema)) {
                schema = dataJson.getString("schema");
            }
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            GaussDB200SourceDTO sourceDTO = GaussDB200SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Gauss_DB200.getVal())
                    .kerberosConfig(confMap)
                    .schema(schema)
                    .build();
            return sourceDTO;
        }
    },
    /**
     * Open_TSDB
     */
    OPEN_TSDB(DataSourceType.OPENTSDB.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            return getSourceDTO(dataJson, confMap, null, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(URL) ? dataJson.getString(URL) : "";
            OpenTSDBSourceDTO sourceDTO = OpenTSDBSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .build();
            return sourceDTO;
        }
    },
    /**
     * SAP_HANA
     */
    SAP_HANA(DataSourceType.SAP_HANA1.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            String schema = dataJson.containsKey(SCHEMA) ? dataJson.getString(SCHEMA) : "";
            return getSourceDTO(dataJson, confMap, schema, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            SapHana1SourceDTO sourceDTO = SapHana1SourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .username(username)
                    .password(password)
                    .schema(schema)
                    .sourceType(DataSourceType.SAP_HANA1.getVal())
                    .kerberosConfig(confMap)
                    .build();
            return sourceDTO;
        }
    },

    /*
     * sybase
     */
    Sybase_jTDS(DataSourceType.Sybase_jTDS.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            String schema = dataJson.containsKey(SCHEMA) ? dataJson.getString(SCHEMA) : "";
            return getSourceDTO(dataJson, confMap, schema, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            return SybaseJTDSSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Sybase_jTDS.getVal())
                    .kerberosConfig(confMap)
                    .build();
        }
    },
    /*
     * sybase_jconn
     */
    Sybase_jConnect(DataSourceType.Sybase_jConnect.getVal()) {
        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig) {
            String schema = dataJson.containsKey(SCHEMA) ? dataJson.getString(SCHEMA) : "";
            return getSourceDTO(dataJson, confMap, schema, expandConfig);
        }

        @Override
        public ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
            String jdbcUrl = dataJson.containsKey(JDBC_URL) ? dataJson.getString(JDBC_URL) : "";
            String username = dataJson.containsKey(JDBC_USERNAME) ? dataJson.getString(JDBC_USERNAME) : "";
            String password = dataJson.containsKey(JDBC_PASSWORD) ? dataJson.getString(JDBC_PASSWORD) : "";
            return SybaseJconnSourceDTO
                    .builder()
                    .url(jdbcUrl)
                    .schema(schema)
                    .username(username)
                    .password(password)
                    .sourceType(DataSourceType.Sybase_jConnect.getVal())
                    .kerberosConfig(confMap)
                    .build();
        }
    },
    ;
    public static final String JDBC_URL = "jdbcUrl";
    public static final String JDBC_USERNAME = "username";
    public static final String JDBC_PASSWORD = "password";
    public static final String SCHEMA = "schema";
    // ssl 认证文件路径
    public static final String SSL_LOCAL_DIR = "sslLocalDir";
    public static final String URL = "url";
    public static final String BROKER_LIST = "brokerList";
    public static final String ADDRESS = "address";

    public static final String HBASECONFIG = "hbaseConfig";

    /**
     * 数据源类型的值
     */
    private Integer val;

    public Integer getVal() {
        return val;
    }

    SourceDTOType(Integer val) {
        this.val = val;
    }

    public abstract ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, Map<String, Object> expandConfig);

    public abstract ISourceDTO getSourceDTO(JSONObject dataJson, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig);


    /**
     * 根据枚举值获取数据源类型
     *
     * @param val
     * @return
     */
    public static SourceDTOType getSourceDTOType(Integer val) {
        for (SourceDTOType sourceDTOType : values()) {
            if (sourceDTOType.val.equals(val)) {
                return sourceDTOType;
            }
        }
        throw new RdosDefineException("数据源类型不存在");
    }

    /**
     * 根据数据源获取对应的sourceDTO，供外调用
     *
     * @param dataJson
     * @param sourceType
     * @param confMap
     * @return
     */
    public static ISourceDTO getSourceDTO(JSONObject dataJson, Integer sourceType, Map<String, Object> confMap, Map<String, Object> expandConfig) {
        SourceDTOType sourceDTOType = getSourceDTOType(sourceType);
        return sourceDTOType.getSourceDTO(dataJson, confMap, expandConfig);
    }

    /**
     * 根据数据源获取对应的sourceDTO，供外调用
     *
     * @param dataJson
     * @param sourceType
     * @param confMap
     * @return
     */
    public static ISourceDTO getSourceDTO(JSONObject dataJson, Integer sourceType, Map<String, Object> confMap, String schema, Map<String, Object> expandConfig) {
        SourceDTOType sourceDTOType = getSourceDTOType(sourceType);
        return sourceDTOType.getSourceDTO(dataJson, confMap, schema, expandConfig);
    }

    protected KafkaSourceDTO buildKafkaSourceDTO(JSONObject dataJson, Map<String, Object> kerberosConfig) {
        return KafkaSourceDTO.builder()
                .brokerUrls(dataJson.getString(BROKER_LIST))
                .url(dataJson.getString(ADDRESS))
                .username(dataJson.getString(JDBC_USERNAME))
                .password(dataJson.getString(JDBC_PASSWORD))
                .kerberosConfig(kerberosConfig).build();
    }
}
