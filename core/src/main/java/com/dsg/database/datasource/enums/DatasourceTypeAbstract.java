package com.dsg.database.datasource.enums;

import java.util.List;

/**
 * @description: 所有模块数据源父类
 * @author: dsg
 * @create:2022/1/17 22:31
 */
public abstract class DatasourceTypeAbstract {
    /**
     * 获取对应模块功能下支持的数据源类型列表
     *
     * @return 根据个功能模块需求返回不同数据源支持类型
     */
    public abstract List<DataSourceTypeEnum> listTypeList();

    /**
     * 当前模块是否支持定义的数据源类型
     *
     * @param var1 数据源类型枚举
     * @return 支持或者不支持
     */
    public abstract boolean isDefinition(DataSourceTypeEnum var1);

    /**
     * 当前模块是否支持定义的数据源类型
     *
     * @param var1 数据源类型字符串
     * @return 支持或者不支持
     */
    public abstract boolean isDefinition(String var1);

}
