package com.dsg.database.datasource.dto.source;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Kudu数据源配置参数
 *
 * <AUTHOR>
 * @date 2022/7/13 15:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KuduDatasourceDTO extends BaseDatasourceDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 集群地址，例如：IP1:Port,IP2:Port,IP3:Port3，多个IP地址用英文逗号隔开
     */
    private String hostPorts;
    /**
     * TODO 其他参数，输入JSON格式的参数，示例及默认参数如下：
     * {
     * "openKerberos":false,
     * "user":"",
     * "keytabPath":"",
     * "workerCount":4,
     * "bossCount":1,
     * "operationTimeout":30000,
     * "adminOperationTimeout":30000
     * }
     */
    private String others;
    /**
     * TODO 开启Kerberos认证
     */
    private String openKerberos;

}
