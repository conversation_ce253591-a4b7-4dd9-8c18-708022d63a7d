package com.dsg.database.datasource.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/9/27
 * @create 2022-09-27-10:30
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class BrokersDTO {
    //brokerId
    private int id = 0;
    //broker host
    private String host = "";
    private int port = 0;
    private String created = "";
    private String modify = "";
    private int jmxPort = 0;
    private boolean jmxPortStatus = false;

}
