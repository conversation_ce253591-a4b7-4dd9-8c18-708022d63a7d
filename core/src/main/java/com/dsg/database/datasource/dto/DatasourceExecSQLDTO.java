package com.dsg.database.datasource.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class DatasourceExecSQLDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要执行的SQL
     */
    private String sql;

    /**
     * 是否是查询语句 默认为true
     */
    private Boolean isQueryStatement = true;

    /**
     * 租户编码
     */
    private String tenantCode;
}
