<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>common-loader</artifactId>
        <groupId>com.dtstack.dtcenter</groupId>
        <version>2.0.0-RELEASE</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>common.loader.mysql8_hive</artifactId>

    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <jar.package.name>mysql8_hive</jar.package.name>
        <jar.name>mysql8_hive</jar.name>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dtstack.dtcenter</groupId>
            <artifactId>common.loader.core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dtstack.dtcenter</groupId>
            <artifactId>common.loader.rdbms</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.19</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <shadedArtifactAttached>false</shadedArtifactAttached>
                            <createDependencyReducedPom>true</createDependencyReducedPom>
                            <promoteTransitiveDependencies>true</promoteTransitiveDependencies>

                            <artifactSet>
                                <excludes>
                                    <exclude>org.slf4j:slf4j-log4j12</exclude>
                                    <exclude>log4j:log4j</exclude>
                                    <exclude>org.slf4j:slf4j-api</exclude>
                                </excludes>
                            </artifactSet>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--拷贝第三方依赖文件到指定目录-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!--target/lib是依赖jar包的输出目录，根据自己喜好配置-->
                            <outputDirectory>target/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <!-- 确保解析到正确的版本 -->
                            <outputAbsoluteArtifactFilename>false</outputAbsoluteArtifactFilename>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                    <!-- 生成依赖清单 -->
                    <execution>
                        <id>list-dedp</id>
                        <phase>package</phase>
                        <goals>
                            <goal>list</goal>
                        </goals>
                        <configuration>
                            <!-- 设置输出文件 -->
                            <includeGroupIds>com.dtstack.dtcenter</includeGroupIds>
                            <outputFile>target/classes/dedplib-dependencies.txt</outputFile>
                            <!-- 确保解析到正确的版本 -->
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                    <execution>
                        <id>list-comm</id>
                        <phase>package</phase>
                        <goals>
                            <goal>list</goal>
                        </goals>
                        <configuration>
                            <!-- 设置输出文件 -->
                            <excludeGroupIds>com.dtstack.dtcenter</excludeGroupIds>
                            <outputFile>target/classes/lib-dependencies.txt</outputFile>
                            <!-- 确保解析到正确的版本 -->
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <!-- here the phase you need -->
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <tasks>
                                <copy file="${basedir}/target/${project.name}-${project.version}.jar"
                                      tofile="${basedir}/../build/pluginLibs/${jar.package.name}/${project.name}-${project.version}.jar" />
                                <!--移动lib下的依赖包到lib下-->
                                <copy todir="${basedir}/../build/pluginLibs/${jar.package.name}/">
                                    <fileset dir="${basedir}/target/lib">
                                        <include name="**/*.jar"/>
                                    </fileset>
                                </copy>
                                <!--移动class的txt到lib下-->
                                <copy todir="${basedir}/../build/pluginLibs/${jar.package.name}/">
                                    <fileset dir="${basedir}/target/classes">
                                        <include name="**/*dependencies.txt"/>
                                    </fileset>
                                </copy>
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>