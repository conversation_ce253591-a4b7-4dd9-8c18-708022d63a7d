/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.hbase;

import com.dtstack.dtcenter.common.loader.common.exception.AbsErrorPattern;
import com.dtstack.dtcenter.common.loader.common.exception.ConnErrorCode;

import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 * date：Created in 下午1:46 2020/11/6
 * company: www.dtstack.com
 */
public class HbaseErrorPattern extends AbsErrorPattern {

    private static final Pattern CANNOT_ACQUIRE_CONNECT = Pattern.compile("(?i)Connection\\s*refused");
    private static final Pattern ZK_NODE_NOT_EXISTS = Pattern.compile("(?i)The\\s*node.*is\\s*not\\s*in\\s*ZooKeeper");
    private static final Pattern ZK_IS_NOT_CONNECT = Pattern.compile("(?i)Can't\\s*get\\s*connection\\s*to\\s*ZooKeeper");
    static {
        PATTERN_MAP.put(ConnErrorCode.CANNOT_ACQUIRE_CONNECT.getCode(), CANNOT_ACQUIRE_CONNECT);
        PATTERN_MAP.put(ConnErrorCode.ZK_NODE_NOT_EXISTS.getCode(), ZK_NODE_NOT_EXISTS);
        PATTERN_MAP.put(ConnErrorCode.ZK_IS_NOT_CONNECT.getCode(), ZK_IS_NOT_CONNECT);
    }
}
